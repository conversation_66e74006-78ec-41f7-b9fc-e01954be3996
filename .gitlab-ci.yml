build image demo:
  image: docker:28.5.2
  services:
    - name: docker:28.5.2-dind
      alias: dockerdaemon
  only:
    refs:
      - demo
  variables:
    DOCKER_HOST: tcp://dockerdaemon:2375/
    # Use the overlayfs driver for improved performance.
    DOCKER_DRIVER: overlay2
    # Disable TLS since we're running inside local network.
    DOCKER_TLS_CERTDIR: ""
  script:
      - cp .env.example .env
      - sed -i "s#KEYCLOAK_URL=.*#KEYCLOAK_URL=$KEYCLOAK_URL#g" .env
      - sed -i "s#KEYCLOAK_REALM=.*#KEYCLOAK_REALM=$DEMO_KEYCLOAK_REALM#g" .env
      - sed -i "s#KEYCLOAK_CLIENT_ID=.*#KEYCLOAK_CLIENT_ID=$DEMO_KEYCLOAK_CLIENT_ID#g" .env
      - sed -i "s#KEYCLOAK_CLIENT_SECRET=.*#KEYCLOAK_CLIENT_SECRET=$DEMO_KEYCLOAK_CLIENT_SECRET#g" .env
      - grep -q '^BASE_URL' .env && sed -i "s#.*BASE_URL=.*#BASE_URL=$DEMO_PORTAL_URL#g" .env || echo "BASE_URL=$DEMO_PORTAL_URL" >> .env
      - grep -q '^PORTAL_URL' .env && sed -i "s#.*PORTAL_URL=.*#PORTAL_URL=$DEMO_PORTAL_URL#g" .env || echo "PORTAL_URL=$DEMO_PORTAL_URL" >> .env
      - grep -q '^API_BASE_URL' .env && sed -i "s#.*API_BASE_URL=.*#API_BASE_URL=$DEMO_API_URL#g" .env || echo "API_BASE_URL=$DEMO_API_URL" >> .env
      - grep -q '^REDIS_KEY_PREFIX' .env && sed -i "s#.*REDIS_KEY_PREFIX=.*#REDIS_KEY_PREFIX=$NUXT_REDIS_KEY_PREFIX#g" .env || echo "REDIS_KEY_PREFIX=$NUXT_REDIS_KEY_PREFIX" >> .env
      - grep -q '^REDIS_USERNAME' .env && sed -i "s#.*REDIS_USERNAME=.*#REDIS_USERNAME=$NUXT_REDIS_USERNAME#g" .env || echo "REDIS_USERNAME=$NUXT_REDIS_USERNAME" >> .env
      - grep -q '^REDIS_PASSWORD' .env && sed -i "s#.*REDIS_PASSWORD=.*#REDIS_PASSWORD=$NUXT_REDIS_PASSWD#g" .env || echo "REDIS_PASSWORD=$NUXT_REDIS_PASSWD" >> .env
      - grep -q '^REDIS_URL' .env && sed -i "s#.*REDIS_URL=.*#REDIS_URL=$REDIS_SINGLE_URL#g" .env || echo "REDIS_URL=$REDIS_SINGLE_URL" >> .env
      - grep -q '^SENTRY_DSN' .env && sed -i "s#.*SENTRY_DSN=.*#SENTRY_DSN=$SENTRY_DSN#g" .env || echo "SENTRY_DSN=$SENTRY_DSN" >> .env
      - grep -q '^SENTRY_ENVIRONMENT' .env && sed -i "s#.*SENTRY_ENVIRONMENT=.*#SENTRY_ENVIRONMENT=demo#g" .env || echo "SENTRY_ENVIRONMENT=demo" >> .env
      - grep -q '^SENTRY_TRACE_PROPAGATION_TARGET' .env && sed -i "s#.*SENTRY_TRACE_PROPAGATION_TARGET=.*#SENTRY_TRACE_PROPAGATION_TARGET=#g" .env || echo "SENTRY_TRACE_PROPAGATION_TARGET=" >> .env
      - grep -q '^SENTRY_AUTH_TOKEN' .env && sed -i "s#.*SENTRY_AUTH_TOKEN=.*#SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN#g" .env || echo "SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN" >> .env
      - grep -q '^SENTRY_ORG' .env && sed -i "s#.*SENTRY_ORG=.*#SENTRY_ORG=$SENTRY_ORG#g" .env || echo "SENTRY_ORG=$SENTRY_ORG" >> .env
      - grep -q '^SENTRY_PROJECT' .env && sed -i "s#.*SENTRY_PROJECT=.*#SENTRY_PROJECT=$SENTRY_PROJECT#g" .env || echo "SENTRY_PROJECT=$SENTRY_PROJECT" >> .env
      - grep -q '^SENTRY_URL' .env && sed -i "s#.*SENTRY_URL=.*#SENTRY_URL=$SENTRY_URL#g" .env || echo "SENTRY_URL=$SENTRY_URL" >> .env
      - grep -q '^SENTRY_TUNNEL' .env && sed -i "s#.*SENTRY_TUNNEL=.*#SENTRY_TUNNEL=$SENTRY_TUNNEL#g" .env || echo "SENTRY_TUNNEL=$SENTRY_TUNNEL" >> .env
      - docker login -u $CI_REGISTRY_USER -p $CI_JOB_TOKEN $CI_REGISTRY
      - docker build -t $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_NAME -t $CI_REGISTRY_IMAGE:demo -f deployment/docker/Dockerfile-drk .
      - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_NAME
      - docker push $CI_REGISTRY_IMAGE:demo

build image stage:
  image: docker:28.5.2
  services:
    - name: docker:28.5.2-dind
      alias: dockerdaemon
  only:
    refs:
      - stage
  variables:
    DOCKER_HOST: tcp://dockerdaemon:2375/
    # Use the overlayfs driver for improved performance.
    DOCKER_DRIVER: overlay2
    # Disable TLS since we're running inside local network.
    DOCKER_TLS_CERTDIR: ""
  script:
      - cp .env.example .env
      - sed -i "s#KEYCLOAK_URL=.*#KEYCLOAK_URL=$KEYCLOAK_URL#g" .env
      - sed -i "s#KEYCLOAK_REALM=.*#KEYCLOAK_REALM=$STAGE_KEYCLOAK_REALM#g" .env
      - sed -i "s#KEYCLOAK_CLIENT_ID=.*#KEYCLOAK_CLIENT_ID=$STAGE_KEYCLOAK_CLIENT_ID#g" .env
      - sed -i "s#KEYCLOAK_CLIENT_SECRET=.*#KEYCLOAK_CLIENT_SECRET=$STAGE_KEYCLOAK_CLIENT_SECRET#g" .env
      - grep -q '^BASE_URL' .env && sed -i "s#.*BASE_URL=.*#BASE_URL=$STAGE_PORTAL_URL#g" .env || echo "BASE_URL=$STAGE_PORTAL_URL" >> .env
      - grep -q '^PORTAL_URL' .env && sed -i "s#.*PORTAL_URL=.*#PORTAL_URL=$STAGE_PORTAL_URL#g" .env || echo "PORTAL_URL=$STAGE_PORTAL_URL" >> .env
      - grep -q '^API_BASE_URL' .env && sed -i "s#.*API_BASE_URL=.*#API_BASE_URL=$STAGE_API_URL#g" .env || echo "API_BASE_URL=$STAGE_API_URL" >> .env
      - grep -q '^REDIS_KEY_PREFIX' .env && sed -i "s#.*REDIS_KEY_PREFIX=.*#REDIS_KEY_PREFIX=$NUXT_REDIS_KEY_PREFIX#g" .env || echo "REDIS_KEY_PREFIX=$NUXT_REDIS_KEY_PREFIX" >> .env
      - grep -q '^REDIS_USERNAME' .env && sed -i "s#.*REDIS_USERNAME=.*#REDIS_USERNAME=$NUXT_REDIS_USERNAME#g" .env || echo "REDIS_USERNAME=$NUXT_REDIS_USERNAME" >> .env
      - grep -q '^REDIS_PASSWORD' .env && sed -i "s#.*REDIS_PASSWORD=.*#REDIS_PASSWORD=$NUXT_REDIS_PASSWD#g" .env || echo "REDIS_PASSWORD=$NUXT_REDIS_PASSWD" >> .env
      - grep -q '^REDIS_URL' .env && sed -i "s#.*REDIS_URL=.*#REDIS_URL=$REDIS_SINGLE_URL#g" .env || echo "REDIS_URL=$REDIS_SINGLE_URL" >> .env
      - grep -q '^SENTRY_DSN' .env && sed -i "s#.*SENTRY_DSN=.*#SENTRY_DSN=$SENTRY_DSN#g" .env || echo "SENTRY_DSN=$SENTRY_DSN" >> .env
      - grep -q '^SENTRY_ENVIRONMENT' .env && sed -i "s#.*SENTRY_ENVIRONMENT=.*#SENTRY_ENVIRONMENT=stage#g" .env || echo "SENTRY_ENVIRONMENT=stage" >> .env
      - grep -q '^SENTRY_TRACE_PROPAGATION_TARGET' .env && sed -i "s#.*SENTRY_TRACE_PROPAGATION_TARGET=.*#SENTRY_TRACE_PROPAGATION_TARGET=#g" .env || echo "SENTRY_TRACE_PROPAGATION_TARGET=" >> .env
      - grep -q '^SENTRY_AUTH_TOKEN' .env && sed -i "s#.*SENTRY_AUTH_TOKEN=.*#SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN#g" .env || echo "SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN" >> .env
      - grep -q '^SENTRY_ORG' .env && sed -i "s#.*SENTRY_ORG=.*#SENTRY_ORG=$SENTRY_ORG#g" .env || echo "SENTRY_ORG=$SENTRY_ORG" >> .env
      - grep -q '^SENTRY_PROJECT' .env && sed -i "s#.*SENTRY_PROJECT=.*#SENTRY_PROJECT=$SENTRY_PROJECT#g" .env || echo "SENTRY_PROJECT=$SENTRY_PROJECT" >> .env
      - grep -q '^SENTRY_URL' .env && sed -i "s#.*SENTRY_URL=.*#SENTRY_URL=$SENTRY_URL#g" .env || echo "SENTRY_URL=$SENTRY_URL" >> .env
      - grep -q '^SENTRY_TUNNEL' .env && sed -i "s#.*SENTRY_TUNNEL=.*#SENTRY_TUNNEL=$SENTRY_TUNNEL#g" .env || echo "SENTRY_TUNNEL=$SENTRY_TUNNEL" >> .env
      - docker login -u $CI_REGISTRY_USER -p $CI_JOB_TOKEN $CI_REGISTRY
      - docker build -t $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_NAME -t $CI_REGISTRY_IMAGE:stage -f deployment/docker/Dockerfile-drk .
      - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_NAME
      - docker push $CI_REGISTRY_IMAGE:stage

build image training:
  image: docker:28.5.2
  services:
    - name: docker:28.5.2-dind
      alias: dockerdaemon
  only:
    refs:
      - training
  variables:
    DOCKER_HOST: tcp://dockerdaemon:2375/
    # Use the overlayfs driver for improved performance.
    DOCKER_DRIVER: overlay2
    # Disable TLS since we're running inside local network.
    DOCKER_TLS_CERTDIR: ""
  script:
      - cp .env.example .env
      - sed -i "s#KEYCLOAK_URL=.*#KEYCLOAK_URL=$KEYCLOAK_URL#g" .env
      - sed -i "s#KEYCLOAK_REALM=.*#KEYCLOAK_REALM=$TRAINING_KEYCLOAK_REALM#g" .env
      - sed -i "s#KEYCLOAK_CLIENT_ID=.*#KEYCLOAK_CLIENT_ID=$TRAINING_KEYCLOAK_CLIENT_ID#g" .env
      - sed -i "s#KEYCLOAK_CLIENT_SECRET=.*#KEYCLOAK_CLIENT_SECRET=$TRAINING_KEYCLOAK_CLIENT_SECRET#g" .env
      - grep -q '^BASE_URL' .env && sed -i "s#.*BASE_URL=.*#BASE_URL=$TRAINING_PORTAL_URL#g" .env || echo "BASE_URL=$TRAINING_PORTAL_URL" >> .env
      - grep -q '^PORTAL_URL' .env && sed -i "s#.*PORTAL_URL=.*#PORTAL_URL=$TRAINING_PORTAL_URL#g" .env || echo "PORTAL_URL=$TRAINING_PORTAL_URL" >> .env
      - grep -q '^API_BASE_URL' .env && sed -i "s#.*API_BASE_URL=.*#API_BASE_URL=$TRAINING_API_URL#g" .env || echo "API_BASE_URL=$TRAINING_API_URL" >> .env
      - grep -q '^REDIS_KEY_PREFIX' .env && sed -i "s#.*REDIS_KEY_PREFIX=.*#REDIS_KEY_PREFIX=$NUXT_REDIS_KEY_PREFIX#g" .env || echo "REDIS_KEY_PREFIX=$NUXT_REDIS_KEY_PREFIX" >> .env
      - grep -q '^REDIS_USERNAME' .env && sed -i "s#.*REDIS_USERNAME=.*#REDIS_USERNAME=$NUXT_REDIS_USERNAME#g" .env || echo "REDIS_USERNAME=$NUXT_REDIS_USERNAME" >> .env
      - grep -q '^REDIS_PASSWORD' .env && sed -i "s#.*REDIS_PASSWORD=.*#REDIS_PASSWORD=$NUXT_REDIS_PASSWD#g" .env || echo "REDIS_PASSWORD=$NUXT_REDIS_PASSWD" >> .env
      - grep -q '^REDIS_URL' .env && sed -i "s#.*REDIS_URL=.*#REDIS_URL=$REDIS_SINGLE_URL#g" .env || echo "REDIS_URL=$REDIS_SINGLE_URL" >> .env
      - grep -q '^SENTRY_DSN' .env && sed -i "s#.*SENTRY_DSN=.*#SENTRY_DSN=$SENTRY_DSN#g" .env || echo "SENTRY_DSN=$SENTRY_DSN" >> .env
      - grep -q '^SENTRY_ENVIRONMENT' .env && sed -i "s#.*SENTRY_ENVIRONMENT=.*#SENTRY_ENVIRONMENT=training#g" .env || echo "SENTRY_ENVIRONMENT=training" >> .env
      - grep -q '^SENTRY_TRACE_PROPAGATION_TARGET' .env && sed -i "s#.*SENTRY_TRACE_PROPAGATION_TARGET=.*#SENTRY_TRACE_PROPAGATION_TARGET=#g" .env || echo "SENTRY_TRACE_PROPAGATION_TARGET=" >> .env
      - grep -q '^SENTRY_AUTH_TOKEN' .env && sed -i "s#.*SENTRY_AUTH_TOKEN=.*#SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN#g" .env || echo "SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN" >> .env
      - grep -q '^SENTRY_ORG' .env && sed -i "s#.*SENTRY_ORG=.*#SENTRY_ORG=$SENTRY_ORG#g" .env || echo "SENTRY_ORG=$SENTRY_ORG" >> .env
      - grep -q '^SENTRY_PROJECT' .env && sed -i "s#.*SENTRY_PROJECT=.*#SENTRY_PROJECT=$SENTRY_PROJECT#g" .env || echo "SENTRY_PROJECT=$SENTRY_PROJECT" >> .env
      - grep -q '^SENTRY_URL' .env && sed -i "s#.*SENTRY_URL=.*#SENTRY_URL=$SENTRY_URL#g" .env || echo "SENTRY_URL=$SENTRY_URL" >> .env
      - grep -q '^SENTRY_TUNNEL' .env && sed -i "s#.*SENTRY_TUNNEL=.*#SENTRY_TUNNEL=$SENTRY_TUNNEL#g" .env || echo "SENTRY_TUNNEL=$SENTRY_TUNNEL" >> .env
      - docker login -u $CI_REGISTRY_USER -p $CI_JOB_TOKEN $CI_REGISTRY
      - docker build -t $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_NAME -t $CI_REGISTRY_IMAGE:training -f deployment/docker/Dockerfile-drk .
      - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_NAME
      - docker push $CI_REGISTRY_IMAGE:training

build image production:
  image: docker:28.5.2
  services:
    - name: docker:28.5.2-dind
      alias: dockerdaemon
  only:
    refs:
      - portal
  variables:
    DOCKER_HOST: tcp://dockerdaemon:2375/
    # Use the overlayfs driver for improved performance.
    DOCKER_DRIVER: overlay2
    # Disable TLS since we're running inside local network.
    DOCKER_TLS_CERTDIR: ""
  script:
      - cp .env.example .env
      - sed -i "s#KEYCLOAK_URL=.*#KEYCLOAK_URL=$KEYCLOAK_URL#g" .env
      - sed -i "s#KEYCLOAK_REALM=.*#KEYCLOAK_REALM=$KEYCLOAK_REALM#g" .env
      - sed -i "s#KEYCLOAK_CLIENT_ID=.*#KEYCLOAK_CLIENT_ID=$KEYCLOAK_CLIENT_ID#g" .env
      - sed -i "s#KEYCLOAK_CLIENT_SECRET=.*#KEYCLOAK_CLIENT_SECRET=$KEYCLOAK_CLIENT_SECRET#g" .env
      - grep -q '^BASE_URL' .env && sed -i "s#.*BASE_URL=.*#BASE_URL=$PRODUCTION_PORTAL_URL#g" .env || echo "BASE_URL=$PRODUCTION_PORTAL_URL" >> .env
      - grep -q '^PORTAL_URL' .env && sed -i "s#.*PORTAL_URL=.*#PORTAL_URL=$PRODUCTION_PORTAL_URL#g" .env || echo "PORTAL_URL=$PRODUCTION_PORTAL_URL" >> .env
      - grep -q '^API_BASE_URL' .env && sed -i "s#.*API_BASE_URL=.*#API_BASE_URL=$PRODUCTION_API_URL#g" .env || echo "API_BASE_URL=$PRODUCTION_API_URL" >> .env
      - grep -q '^REDIS_KEY_PREFIX' .env && sed -i "s#.*REDIS_KEY_PREFIX=.*#REDIS_KEY_PREFIX=$NUXT_REDIS_KEY_PREFIX#g" .env || echo "REDIS_KEY_PREFIX=$NUXT_REDIS_KEY_PREFIX" >> .env
      - grep -q '^REDIS_SENTINEL_USERNAME' .env && sed -i "s#.*REDIS_USERNAME=.*#REDIS_USERNAME=$NUXT_REDIS_USERNAME#g" .env || echo "REDIS_USERNAME=$NUXT_REDIS_USERNAME" >> .env
      - grep -q '^REDIS_SENTINEL_PASSWORD' .env && sed -i "s#.*REDIS_PASSWORD=.*#REDIS_PASSWORD=$NUXT_REDIS_PASSWD#g" .env || echo "REDIS_PASSWORD=$NUXT_REDIS_PASSWD" >> .env
      - grep -q '^REDIS_SENTINEL_NAME' .env && sed -i "s#.*REDIS_SENTINEL_NAME=.*#REDIS_SENTINEL_NAME=$PRODUCTION_REDIS_SENTINEL_MASTER#g" .env || echo "REDIS_SENTINEL_NAME=$PRODUCTION_REDIS_SENTINEL_MASTER" >> .env
      - grep -q '^REDIS_SENTINELS' .env && sed -i "s#.*REDIS_SENTINELS=.*#REDIS_SENTINELS=$PRODUCTION_REDIS_SENTINEL_NODES#g" .env || echo "REDIS_SENTINELS=$PRODUCTION_REDIS_SENTINEL_NODES" >> .env
      - grep -q '^REDIS_URL' .env && sed -i "s#.*REDIS_URL=.*#\#REDIS_URL=#g" .env || echo "#REDIS_URL=" >> .env
      - grep -q '^SENTRY_DSN' .env && sed -i "s#.*SENTRY_DSN=.*#SENTRY_DSN=$SENTRY_DSN#g" .env || echo "SENTRY_DSN=$SENTRY_DSN" >> .env
      - grep -q '^SENTRY_ENVIRONMENT' .env && sed -i "s#.*SENTRY_ENVIRONMENT=.*#SENTRY_ENVIRONMENT=production#g" .env || echo "SENTRY_ENVIRONMENT=production" >> .env
      - grep -q '^SENTRY_TRACE_PROPAGATION_TARGET' .env && sed -i "s#.*SENTRY_TRACE_PROPAGATION_TARGET=.*#SENTRY_TRACE_PROPAGATION_TARGET=#g" .env || echo "SENTRY_TRACE_PROPAGATION_TARGET=" >> .env
      - grep -q '^SENTRY_AUTH_TOKEN' .env && sed -i "s#.*SENTRY_AUTH_TOKEN=.*#SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN#g" .env || echo "SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN" >> .env
      - grep -q '^SENTRY_ORG' .env && sed -i "s#.*SENTRY_ORG=.*#SENTRY_ORG=$SENTRY_ORG#g" .env || echo "SENTRY_ORG=$SENTRY_ORG" >> .env
      - grep -q '^SENTRY_PROJECT' .env && sed -i "s#.*SENTRY_PROJECT=.*#SENTRY_PROJECT=$SENTRY_PROJECT#g" .env || echo "SENTRY_PROJECT=$SENTRY_PROJECT" >> .env
      - grep -q '^SENTRY_URL' .env && sed -i "s#.*SENTRY_URL=.*#SENTRY_URL=$SENTRY_URL#g" .env || echo "SENTRY_URL=$SENTRY_URL" >> .env
      - grep -q '^SENTRY_TUNNEL' .env && sed -i "s#.*SENTRY_TUNNEL=.*#SENTRY_TUNNEL=$SENTRY_TUNNEL#g" .env || echo "SENTRY_TUNNEL=$SENTRY_TUNNEL" >> .env
      - docker login -u $CI_REGISTRY_USER -p $CI_JOB_TOKEN $CI_REGISTRY
      - docker build -t $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_NAME -t $CI_REGISTRY_IMAGE:production -f deployment/docker/Dockerfile-drk .
      - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_NAME
      - docker push $CI_REGISTRY_IMAGE:production

build image backend_test:
  image: docker:28.5.2
  services:
    - name: docker:28.5.2-dind
      alias: dockerdaemon
  only:
    refs:
      - backend_test
  variables:
    DOCKER_HOST: tcp://dockerdaemon:2375/
    # Use the overlayfs driver for improved performance.
    DOCKER_DRIVER: overlay2
    # Disable TLS since we're running inside local network.
    DOCKER_TLS_CERTDIR: ""
  script:
      - cp .env.example .env
      - sed -i "s#KEYCLOAK_URL=.*#KEYCLOAK_URL=$BACKEND_TEST_KEYCLOAK_URL#g" .env
      - sed -i "s#KEYCLOAK_REALM=.*#KEYCLOAK_REALM=$BACKEND_TEST_KEYCLOAK_REALM#g" .env
      - sed -i "s#KEYCLOAK_CLIENT_ID=.*#KEYCLOAK_CLIENT_ID=$BACKEND_TEST_KEYCLOAK_CLIENT_ID#g" .env
      - sed -i "s#KEYCLOAK_CLIENT_SECRET=.*#KEYCLOAK_CLIENT_SECRET=$BACKEND_TEST_KEYCLOAK_CLIENT_SECRET#g" .env
      - grep -q '^BASE_URL' .env && sed -i "s#.*BASE_URL=.*#BASE_URL=$BACKEND_TEST_PORTAL_URL#g" .env || echo "BASE_URL=$BACKEND_TEST_PORTAL_URL" >> .env
      - grep -q '^PORTAL_URL' .env && sed -i "s#.*PORTAL_URL=.*#PORTAL_URL=$BACKEND_TEST_PORTAL_URL#g" .env || echo "PORTAL_URL=$BACKEND_TEST_PORTAL_URL" >> .env
      - grep -q '^API_BASE_URL' .env && sed -i "s#.*API_BASE_URL=.*#API_BASE_URL=$BACKEND_TEST_API_URL#g" .env || echo "API_BASE_URL=$BACKEND_TEST_API_URL" >> .env
      - grep -q '^REDIS_KEY_PREFIX' .env && sed -i "s#.*REDIS_KEY_PREFIX=.*#REDIS_KEY_PREFIX=$NUXT_REDIS_KEY_PREFIX#g" .env || echo "REDIS_KEY_PREFIX=$NUXT_REDIS_KEY_PREFIX" >> .env
      - grep -q '^REDIS_USERNAME' .env && sed -i "s#.*REDIS_USERNAME=.*#REDIS_USERNAME=$NUXT_REDIS_USERNAME#g" .env || echo "REDIS_USERNAME=$NUXT_REDIS_USERNAME" >> .env
      - grep -q '^REDIS_PASSWORD' .env && sed -i "s#.*REDIS_PASSWORD=.*#REDIS_PASSWORD=$NUXT_REDIS_PASSWD#g" .env || echo "REDIS_PASSWORD=$NUXT_REDIS_PASSWD" >> .env
      - grep -q '^REDIS_URL' .env && sed -i "s#.*REDIS_URL=.*#REDIS_URL=$REDIS_SINGLE_URL#g" .env || echo "REDIS_URL=$REDIS_SINGLE_URL" >> .env
      - grep -q '^SENTRY_DSN' .env && sed -i "s#.*SENTRY_DSN=.*#SENTRY_DSN=$SENTRY_DSN#g" .env || echo "SENTRY_DSN=$SENTRY_DSN" >> .env
      - grep -q '^SENTRY_ENVIRONMENT' .env && sed -i "s#.*SENTRY_ENVIRONMENT=.*#SENTRY_ENVIRONMENT=backend_test#g" .env || echo "SENTRY_ENVIRONMENT=backend_test" >> .env
      - grep -q '^SENTRY_TRACE_PROPAGATION_TARGET' .env && sed -i "s#.*SENTRY_TRACE_PROPAGATION_TARGET=.*#SENTRY_TRACE_PROPAGATION_TARGET=#g" .env || echo "SENTRY_TRACE_PROPAGATION_TARGET=" >> .env
      - grep -q '^SENTRY_AUTH_TOKEN' .env && sed -i "s#.*SENTRY_AUTH_TOKEN=.*#SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN#g" .env || echo "SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN" >> .env
      - grep -q '^SENTRY_ORG' .env && sed -i "s#.*SENTRY_ORG=.*#SENTRY_ORG=$SENTRY_ORG#g" .env || echo "SENTRY_ORG=$SENTRY_ORG" >> .env
      - grep -q '^SENTRY_PROJECT' .env && sed -i "s#.*SENTRY_PROJECT=.*#SENTRY_PROJECT=$SENTRY_PROJECT#g" .env || echo "SENTRY_PROJECT=$SENTRY_PROJECT" >> .env
      - grep -q '^SENTRY_URL' .env && sed -i "s#.*SENTRY_URL=.*#SENTRY_URL=$SENTRY_URL#g" .env || echo "SENTRY_URL=$SENTRY_URL" >> .env
      - docker login -u $CI_REGISTRY_USER -p $CI_JOB_TOKEN $CI_REGISTRY
      - docker build -t $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_NAME -t $CI_REGISTRY_IMAGE:test -f deployment/docker/Dockerfile-drk .
      - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_NAME
      - docker push $CI_REGISTRY_IMAGE:test

build image backend_dev:
  image: docker:28.5.2
  services:
    - name: docker:28.5.2-dind
      alias: dockerdaemon
  only:
    refs:
      - backend_dev
  variables:
    DOCKER_HOST: tcp://dockerdaemon:2375/
    # Use the overlayfs driver for improved performance.
    DOCKER_DRIVER: overlay2
    # Disable TLS since we're running inside local network.
    DOCKER_TLS_CERTDIR: ""
  script:
      - cp .env.example .env
      - sed -i "s#KEYCLOAK_URL=.*#KEYCLOAK_URL=$BACKEND_DEV_KEYCLOAK_URL#g" .env
      - sed -i "s#KEYCLOAK_REALM=.*#KEYCLOAK_REALM=$BACKEND_DEV_KEYCLOAK_REALM#g" .env
      - sed -i "s#KEYCLOAK_CLIENT_ID=.*#KEYCLOAK_CLIENT_ID=$BACKEND_DEV_KEYCLOAK_CLIENT_ID#g" .env
      - sed -i "s#KEYCLOAK_CLIENT_SECRET=.*#KEYCLOAK_CLIENT_SECRET=$BACKEND_DEV_KEYCLOAK_CLIENT_SECRET#g" .env
      - grep -q '^BASE_URL' .env && sed -i "s#.*BASE_URL=.*#BASE_URL=$BACKEND_DEV_PORTAL_URL#g" .env || echo "BASE_URL=$BACKEND_DEV_PORTAL_URL" >> .env
      - grep -q '^PORTAL_URL' .env && sed -i "s#.*PORTAL_URL=.*#PORTAL_URL=$BACKEND_DEV_PORTAL_URL#g" .env || echo "PORTAL_URL=$BACKEND_DEV_PORTAL_URL" >> .env
      - grep -q '^API_BASE_URL' .env && sed -i "s#.*API_BASE_URL=.*#API_BASE_URL=$BACKEND_DEV_API_URL#g" .env || echo "API_BASE_URL=$BACKEND_DEV_API_URL" >> .env
      - grep -q '^REDIS_KEY_PREFIX' .env && sed -i "s#.*REDIS_KEY_PREFIX=.*#REDIS_KEY_PREFIX=$NUXT_REDIS_KEY_PREFIX#g" .env || echo "REDIS_KEY_PREFIX=$NUXT_REDIS_KEY_PREFIX" >> .env
      - grep -q '^REDIS_USERNAME' .env && sed -i "s#.*REDIS_USERNAME=.*#REDIS_USERNAME=$NUXT_REDIS_USERNAME#g" .env || echo "REDIS_USERNAME=$NUXT_REDIS_USERNAME" >> .env
      - grep -q '^REDIS_PASSWORD' .env && sed -i "s#.*REDIS_PASSWORD=.*#REDIS_PASSWORD=$NUXT_REDIS_PASSWD#g" .env || echo "REDIS_PASSWORD=$NUXT_REDIS_PASSWD" >> .env
      - grep -q '^REDIS_URL' .env && sed -i "s#.*REDIS_URL=.*#REDIS_URL=$REDIS_SINGLE_URL#g" .env || echo "REDIS_URL=$REDIS_SINGLE_URL" >> .env
      - grep -q '^SENTRY_DSN' .env && sed -i "s#.*SENTRY_DSN=.*#SENTRY_DSN=$SENTRY_DSN#g" .env || echo "SENTRY_DSN=$SENTRY_DSN" >> .env
      - grep -q '^SENTRY_ENVIRONMENT' .env && sed -i "s#.*SENTRY_ENVIRONMENT=.*#SENTRY_ENVIRONMENT=backend_dev#g" .env || echo "SENTRY_ENVIRONMENT=backend_dev" >> .env
      - grep -q '^SENTRY_TRACE_PROPAGATION_TARGET' .env && sed -i "s#.*SENTRY_TRACE_PROPAGATION_TARGET=.*#SENTRY_TRACE_PROPAGATION_TARGET=#g" .env || echo "SENTRY_TRACE_PROPAGATION_TARGET=" >> .env
      - grep -q '^SENTRY_AUTH_TOKEN' .env && sed -i "s#.*SENTRY_AUTH_TOKEN=.*#SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN#g" .env || echo "SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN" >> .env
      - grep -q '^SENTRY_ORG' .env && sed -i "s#.*SENTRY_ORG=.*#SENTRY_ORG=$SENTRY_ORG#g" .env || echo "SENTRY_ORG=$SENTRY_ORG" >> .env
      - grep -q '^SENTRY_PROJECT' .env && sed -i "s#.*SENTRY_PROJECT=.*#SENTRY_PROJECT=$SENTRY_PROJECT#g" .env || echo "SENTRY_PROJECT=$SENTRY_PROJECT" >> .env
      - grep -q '^SENTRY_URL' .env && sed -i "s#.*SENTRY_URL=.*#SENTRY_URL=$SENTRY_URL#g" .env || echo "SENTRY_URL=$SENTRY_URL" >> .env
      - docker login -u $CI_REGISTRY_USER -p $CI_JOB_TOKEN $CI_REGISTRY
      - docker build -t $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_NAME -t $CI_REGISTRY_IMAGE:dev -f deployment/docker/Dockerfile-drk .
      - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_NAME
      - docker push $CI_REGISTRY_IMAGE:dev
