<template>
    <Combobox v-bind="$props" class="relative group/listbox" as="div">
        <div ref="reference" class="flex w-full">
            <!-- When we do not use input in for filtering opitons
            then this one is hidden behind button because headlesui keep focus on the input
            so we need it for accesibility purpouses-->
            <ComboboxInput class="absolute listbox-button" v-if="withInput" tabindex="-1"/>
            <ComboboxButton ref="button" class="listbox-button z-10" v-slot="{ open, value }" as="button" tabindex="0">
                <slot name="button" v-bind="{ open, value }"> Bitte wählen </slot>
            </ComboboxButton>
        </div>
        <div ref="popper" class="absolute z-50 w-full">
            <ComboboxOptions class="mt-1 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                <slot name="header" />
                <UiScrollarea class="text-base max-h-72" :currentPage="currentPage">
                    <slot />
                </UiScrollarea>
                <slot name="footer" />
            </ComboboxOptions>
        </div>
    </Combobox>
</template>

<script lang="ts" setup>
    import { Combobox, ComboboxButton, ComboboxOptions, ComboboxInput } from '@headlessui/vue'

    const props = withDefaults(defineProps<{
            currentPage?: number
            withInput?: boolean
            placement?: 'bottom-start' | 'bottom-end' | 'top-start' | 'auto-start'
        }>(),{
            withInput: true,
            placement: 'auto-start'
        })

    defineSlots<{
        default: (props: {}) => any
        button?: (props: { open: boolean; value: ApiModel<'CodeEntry'> }) => any
        search?: (props: {}) => any
        header?: (props: {}) => any
        footer?: (props: {}) => any
        input?: (props: {}) => any
    }>()

    const reference = ref<HTMLElement>()
    const popper = ref<HTMLElement>()

    usePopper(reference, popper, { placement: props.placement })
</script>

<style lang="pcss" scoped>
    .listbox-button {
        @apply form-input w-full cursor-default pr-7 h-full;
        @apply icon-selector-gray-400 bg-no-repeat bg-[right_0.125rem_center] bg-[length:1.25rem_1.25rem];

        .group\/listbox  &[data-headlessui-state~=open] {
            @apply ui-open:icon-selector-blue-500
        }
    }

    .listbox-options {
        @apply max-h-60 overflow-auto
    }
</style>
