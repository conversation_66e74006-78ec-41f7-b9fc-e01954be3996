import { createQueryKeys, mergeQueryKeys } from '@lukemorales/query-key-factory'
import ListType from '~~/enums/listType'

export type ListResponse<T = any> = {
    totalItems: number
    offset: number
    limit: number
    items: T[]
}


// type InternalPublicationType = 'ALL' | 'NONE' | 'BER' | 'BW' | 'JRK' | 'WW' | 'WUS'

export const eventQueries = createQueryKeys('events', {
    /**
     * The filtered list of events
     */
    list: (params: any) => ({
        queryKey: [{ params }],
        queryFn: async (ctx) => {
            // Normalize values: Especially needed for dates that are transmitted as quoted strings otherwise.
            // Like that: ?date="2022-11-30T23:00:00.000Z"
            const searchParams = JSON.parse(JSON.stringify(unref(params)))

            const list = await $fetch<ListResponse>('/api/events', {
                query: searchParams
            })

            const items = useModelFactory('Event').fromJson(list.items || [])

            return {
                ...list,
                items
            }
        }
    }),

    /**
     * The event details
     */
    detail: (eventId: number) => ({
        queryKey: [eventId],
        queryFn: async (_ctx) => {
            return useModelFactory('Event').fromJson(
                // Fetch...
                await $fetch<object>(`/api/events/${eventId}`)
            )
        },

        contextQueries: {
            /**
             * The list of registered people
             */
            registrations: () => ({
                queryKey: [null],
                queryFn: async () => {
                    return useModelFactory('EventRegistration').fromJson(
                        // Fetch...
                        await $fetch<object[]>(`/api/events/${eventId}/registrations`)
                    )
                }
            }),

            /**
             * The list of members that are allowed to register participants for this event
             */
            registrators: () => ({
                queryKey: [null],
                queryFn: async (ctx) => {
                    // Fetch...
                    const { items } = await $fetch<ListResponse>(`/api/events/${eventId}/registrators`)
                    return useModelFactory('MemberData').fromJson(items)
                }
            }),

            /**
             * The list of locations of this event,
             */
            locations: () => ({
                queryKey: [null],
                queryFn: async (_ctx) => {
                    return useModelFactory('EventAddress').fromJson(
                        //...
                        await $fetch<object[]>(`/api/events/${eventId}/locations`)
                    )
                }
            }),

            /**
             * The organizer of this event
             */
            organizer: () => ({
                queryKey: [null],
                queryFn: async (_ctx) => {
                    return useModelFactory('ComplexAddressContact').fromJson(
                        //...
                        await $fetch<object>(`/api/events/${eventId}/organizer`)
                    )
                }
            }),

            /**
             * The responsible persons for this event
             */
            responsibles: () => ({
                queryKey: [null],
                queryFn: async (_ctx) => {
                    return useModelFactory('MemberData').fromJson(
                        //...
                        await $fetch<object[]>(`/api/events/${eventId}/responsibles`)
                    )
                }
            }),

            /**
             * The list of drresscodes for this event,
             */
            dresscodes: () => ({
                queryKey: [null],
                queryFn: async (_ctx) => {
                    return useModelFactory('CodeEntry').fromJson(
                        //...
                        await $fetch<object[]>(`/api/events/${eventId}/dresscodes`)
                    )
                }
            }),

            /**
             * The list of catering settings for this event,
             */
            caterings: () => ({
                queryKey: [null],
                queryFn: async (_ctx) => {
                    return useModelFactory('CodeEntry').fromJson(
                        //...
                        await $fetch<object[]>(`/api/events/${eventId}/caterings`)
                    )
                }
            }),

            /**
             * The list of catering settings for this event,
             */
            documents: () => ({
                queryKey: [null],
                queryFn: async (_ctx) => {
                    return useModelFactory('EventDocumentMeta').fromJson(
                        //...
                        await $fetch<object[]>(`/api/events/${eventId}/documents`)
                    )
                }
            }),

            /**
             * The publication status of this event,
             */
            internalPublication: (canEdit?: boolean) => ({
                queryKey: [null],
                queryFn: (_ctx) => $fetch<ApiModel<'InternalPublication'>[]>(`/api/events/${eventId}/internal-publication`)
            }),

            /**
             * Recieve all event posts of an event
             */
            eventPosts: () => ({
                queryKey: ['all'],
                queryFn: async (_ctx) => {
                    // Fetch data...
                    return useModelFactory('EventPost').fromJson(
                        // Fetch data...
                        await $fetch<any[]>(`/api/events/${eventId}/event-posts`)
                    )
                }
            }),

            /**
             * Recieve all signing ups of an event
             */
            signingUps: () => ({
                queryKey: ['all'],
                queryFn: async (_ctx) => {
                    // Fetch data...
                    const list = await $fetch<ListResponse>(`/api/events/${eventId}/signing-ups`)
                    return useModelFactory('SigningUp').fromJson(
                        // Extract items
                        list.items
                    )
                }
            }),

            /**
             * Recieve resource settings for the given event
             */
            resourceSettings: () => ({
                queryKey: [null],
                queryFn: async (_ctx) => {
                    return useModelFactory('ResourceSetting').fromJson(
                        // Fetch...
                        await $fetch<ApiModel<'ResourceSetting'>[]>(`/api/events/${eventId}/resource-settings`)
                    )
                }
            }),

            /**
             * Conflicts for the event posts in question
             */
            conflicts: () => ({
                queryKey: [null],
                queryFn: (_ctx) =>
                    $fetch(`/api/events/${eventId}/event-posts/conflicts`, {
                        query: { eventPosts: [] }
                    })
            }),

            /**
             * Tags for the event posts
             */
            tags: () => ({
                queryKey: [null],
                queryFn: async (_ctx) => {
                    const data = await $fetch<ApiModel<'Event'>>(`/api/events/${eventId}`)
                    return useModelFactory('CodeEntry').fromJson(data.eventTags)
                },
            }),
        }
    })
})

export const memberQueries = createQueryKeys('members', {
    /**
     * Get details of a specific member
     */
    detail: (memberId: number) => ({
        queryKey: [memberId],
        queryFn: async (_ctx) => {
            try {
                return useModelFactory('Masterdata').fromJson(
                    // Fetch...
                    await $fetch<ApiModel<'Masterdata'>>(`/api/members/${memberId}/masterdata`)
                )
            } catch (error) {
                if (error.status === 403)
                    return useModelFactory('Masterdata').fromJson({ basicData: null })
            }
        },

        contextQueries: {
            /**
             * Get driving licenses of specified member.
             *
             * Within events this is needed to show if this member is qualified for an event post.
             */
            drivingLicenses: () => ({
                queryKey: ['all'],
                queryFn: () => $fetch(`/api/members/${memberId}/driver-licenses`)
            }),

            /**
             * Retrieves a list of code entries.
             *
             * This is what we actually use in event module
             *
             * Everything but the actual code entries is stripped away from response
             */
            drivingLicensesByType: (type: 'street') => ({
                queryKey: [type],
                queryFn: async () => {
                    // Fetch all code entries by type
                    const license = await $fetch(`/api/members/${memberId}/driver-licenses/${type}`)

                    // Extract code entries for licenseClass
                    const codeEntries = license?.acquiredLicenseClasses?.map(({ licenseClass }) => licenseClass) || []

                    return codeEntries
                }
            }),

            /**
             * Fetch operation qualifications
             */
            operationQualifications: () => ({
                queryKey: [null],
                queryFn: async () => {
                    return useModelFactory('OperationQualification').fromJson(
                        await $fetch<object[]>(`/api/members/${memberId}/operation-qualification`)
                    )
                }
            })
        }
    })
})

export const codeEntryQueries = createQueryKeys('codeEntries', {
    codeEntry: (listType: ListType | ListType[]) => ({
        queryKey: Array.isArray(listType) ? ['code-entries', listType.join(',')] : ['code-entries', listType],
        queryFn: () => Array.isArray(listType)
            ? $fetch(`/api/code-entries/`, { method: 'get', query: { type: ListType } })
            : $fetch(`/api/code-entries/${listType}`),
    }),
})

export const queries = mergeQueryKeys(eventQueries, memberQueries, codeEntryQueries)
