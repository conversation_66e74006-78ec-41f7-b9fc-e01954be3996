import { QueryClient, useQuery, useQueryClient } from '@tanstack/vue-query'
import { isQueryKey } from '@tanstack/vue-query/build/lib/utils'

type EventQueryContext = ReturnType<typeof eventQueries.detail>['_ctx']

type ContextKey = keyof EventQueryContext

type ContextQueryParams<T extends ContextKey> = Parameters<EventQueryContext[T]>

// Uff
type ListQueryResult = Awaited<ReturnType<ReturnType<typeof eventQueries.list>['queryFn']>>

class EventQuery extends Function {
    private queryClient: QueryClient = null

    constructor(private event: number) {
        super()

        this.queryClient = useQueryClient()

        return new Proxy(this, {
            apply: (target, _thisArg, _args): ReturnType<EventQuery['details']> => target.details()
        })
    }

    public details = () => {

        return useQuery({
            ...eventQueries.detail(this.event),
            staleTime: Infinity,
            cacheTime: Infinity,
            retry: (failureCount: number, error: any) => {
                const status = error?.response?.status
                if (status === 403 || status === 410) return false
                return failureCount < 3
            },

            // We have to walk through all lists (filters, paginations etc.) in order to find the item
            initialData: () => {
                const matches = this.queryClient.getQueriesData<ListQueryResult>(eventQueries.list._def)

                for (const [_key, list] of matches) {
                    const item = list?.items.find(({ id }) => id === this.event)
                    if (item) {
                        return item
                    }
                }

                return undefined
            },
            // We have to walk through all matching queries to identify the correct updated at property
            initialDataUpdatedAt: () => {
                const matches = this.queryClient.getQueriesData<ListQueryResult>(eventQueries.list._def)
                for (const [key, list] of matches) {
                    const item = list?.items.find(({ id }) => id === this.event)
                    if (item) {
                        const updatedAt = this.queryClient.getQueryState(key, { exact: true })?.dataUpdatedAt
                        return updatedAt
                    }
                }

                return undefined
            }
        })
    }

    public locations = (...args: ContextQueryParams<'locations'>) => {
        return useQuery({
            ...eventQueries.detail(this.event)._ctx['locations'](...args),
            staleTime: Infinity,
            cacheTime: Infinity,
            placeholderData: [],
            initialData: () => {
                const event = this.queryClient.getQueryData<ApiModel<'Event'>>(eventQueries.detail(this.event).queryKey)
                return event?.locations
            }
        })
    }

    public organizer = (...args: ContextQueryParams<'organizer'>) => {
        return useQuery({
            ...eventQueries.detail(this.event)._ctx['organizer'](...args),
            staleTime: Infinity,
            cacheTime: Infinity,
            initialData: () => {
                const event = this.queryClient.getQueryData<ApiModel<'Event'>>(eventQueries.detail(this.event).queryKey)
                return event?.organizer
            }
        })
    }

    public responsibles = (...args: ContextQueryParams<'organizer'>) => {
        return useQuery({
            ...eventQueries.detail(this.event)._ctx['responsibles'](...args),
            staleTime: Infinity,
            cacheTime: Infinity,
            placeholderData: [],
            initialData: () => {
                const event = this.queryClient.getQueryData<ApiModel<'Event'>>(eventQueries.detail(this.event).queryKey)
                return event?.responsibles.map(({ masterdata }) => masterdata)
            }
        })
    }

    public dresscodes = (...args: ContextQueryParams<'dresscodes'>) => {
        return useQuery({
            ...eventQueries.detail(this.event)._ctx['dresscodes'](...args),
            staleTime: Infinity,
            cacheTime: Infinity,
            placeholderData: [],
            initialData: () => {
                const event = this.queryClient.getQueryData<ApiModel<'Event'>>(eventQueries.detail(this.event).queryKey)
                return event?.dressCodes
            }
        })
    }

    public caterings = (...args: ContextQueryParams<'caterings'>) => {
        return useQuery({
            ...eventQueries.detail(this.event)._ctx['caterings'](...args),
            staleTime: Infinity,
            cacheTime: Infinity,
            placeholderData: [],
            initialData: () => {
                const event = this.queryClient.getQueryData<ApiModel<'Event'>>(eventQueries.detail(this.event).queryKey)
                return event?.caterings
            }
        })
    }

    public documents = (...args: ContextQueryParams<'documents'>) => {
        return useQuery({
            ...eventQueries.detail(this.event)._ctx['documents'](...args),
            staleTime: Infinity,
            cacheTime: Infinity,
            placeholderData: [],
            initialData: () => {
                const event = this.queryClient.getQueryData<ApiModel<'Event'>>(eventQueries.detail(this.event).queryKey)
                return event?.documents
            }
        })
    }

    public registrations = (...args: ContextQueryParams<'registrations'>) => {
        return useQuery({
            ...eventQueries.detail(this.event)._ctx['registrations'](...args),
            staleTime: Infinity,
            cacheTime: Infinity,
            placeholderData: []
        })
    }

    public registrators = (...args: ContextQueryParams<'registrators'>) => {
        return useQuery({
            ...eventQueries.detail(this.event)._ctx['registrators'](...args),
            staleTime: Infinity,
            cacheTime: Infinity,
        })
    }

    public internalPublication = (...args: ContextQueryParams<'internalPublication'>) => {
        const canEdit = args[0]
        return useQuery({
            ...eventQueries.detail(this.event)._ctx['internalPublication'](...args),
            enabled: canEdit,
            staleTime: Infinity,
            cacheTime: Infinity,
            retry: (failureCount: number, error: any) => {
                const status = error?.response?.status

                if (status === 403) return false
                return failureCount < 3
            }
        })
    }

    public eventPosts = (...args: ContextQueryParams<'eventPosts'>) => {
        return useQuery({
            ...eventQueries.detail(this.event)._ctx['eventPosts'](...args),
            staleTime: Infinity,
            cacheTime: Infinity,
            placeholderData: [],
            initialData: () => {
                const event = this.queryClient.getQueryData<ApiModel<'Event'>>(eventQueries.detail(this.event).queryKey)
                return event?.eventPosts.filter(({ eventPostResourceType }) => {
                    // DEL-254 – Exclude 'GROUP' ans 'ROOM'
                    return ['PERSONAL', 'TECHNIC'].includes(eventPostResourceType)
                })
            }
        })
    }

    public signingUps = (...args: ContextQueryParams<'signingUps'>) => {
        return useQuery({
            ...eventQueries.detail(this.event)._ctx['signingUps'](...args),
            staleTime: Infinity,
            cacheTime: Infinity,
            placeholderData: [],
            refetchOnMount: 'always',
            initialData: () => {
                const event = this.queryClient.getQueryData<ApiModel<'Event'>>(eventQueries.detail(this.event).queryKey)
                return event?.allSigningUps?.items
            }
        })
    }

    public conflicts = (...args: ContextQueryParams<'conflicts'>) => {
        return useQuery({
            ...eventQueries.detail(this.event)._ctx['conflicts'](...args),
            staleTime: Infinity,
            cacheTime: Infinity,
            placeholderData: [],
            retry: (failureCount: number, error: any) => {
                const status = error?.response?.status

                if (status === 403) return false
                return failureCount < 3
            }
        })
    }

    public resourceSettings = (...args: ContextQueryParams<'resourceSettings'>) => {
        return useQuery({
            ...eventQueries.detail(this.event)._ctx['resourceSettings'](...args),
            staleTime: Infinity,
            cacheTime: Infinity,
            placeholderData: [],
            retry: (failureCount: number, error: any) => {
                const status = error?.response?.status

                if (status === 403) return false
                return failureCount < 3
            }
        })
    }

    public tags = (...args: ContextQueryParams<'tags'>) => {
        return useQuery({
            ...eventQueries.detail(this.event)._ctx['tags'](...args),
            staleTime: Infinity,
            cacheTime: Infinity,
            placeholderData: [],
            initialData: () => {
                const event = this.queryClient.getQueryData<ApiModel<'Event'>>(eventQueries.detail(this.event).queryKey)
                return event?.eventTags
            }
        })
    }

}

export const EventQueryKey = Symbol() as InjectionKey<ReturnType<typeof useEventQueries>>

export function useEventQueries(event: number) {
    return new EventQuery(event)
}

export function provideEventQueries(event: number, eventQuery?: EventQuery) {
    provide(EventQueryKey, eventQuery ?? useEventQueries(event))
}

export function useEventQuery<T extends keyof EventQuery>(query: T, ...args: Parameters<EventQuery[T]>): ReturnType<EventQuery[T]> {
    return inject(EventQueryKey)[query](...args)
}
