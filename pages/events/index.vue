<template>
    <div class="flex w-full flex-1 flex-col gap-y-4">
        <div
            class="flex flex-none flex-wrap items-end gap-x-2 gap-y-6 px-4 sm:px-0 lg:flex-nowrap lg:gap-y-0"
            :class="{'mt-[13px] items-center' : currentView.value !== 'list'}"
        >
            <EventFilterInterval v-model="filter" v-if="currentView.value === 'list'"/>

            <button class="form-button button-outline items-center" @click="openFilterSettings">
                <IconAdjustments class="h-4 w-4" />
                Filter
            </button>
            <button class="items-center form-button button-outline" @click="refetch()" v-if="currentView.value === 'list'">
                <IconRefresh class="w-4 h-4" />
                Neu laden
            </button>
            <CalendarHeader
                v-if="currentView.value === 'month' || currentView.value === 'week'"
                :view="currentView.value"
            />
            <UiMenu
                :without-default-css="true"
                class="xl:mr-20"
                :class="[
                    {'md:ml-auto' : currentView.value == 'list'},
                ]"
            >
                <template #button>
                    <button
                        type="button"
                        class="flex rounded-full items-center w-40 justify-between bg-white px-3 py-2.5 text-grey-900 shadow-sm ring-1 ring-inset ring-grey-300 hover:bg-grey-50">
                        {{ currentView.label}}
                        <UiIcon name="chevron-down" class="-mr-1 h-5 w-5 text-grey-400" aria-hidden="true" />
                    </button>
                </template>
                 <UiMenuItem v-for="{ label, id } in viewOptions" :key="id" @click="currentView = viewOptions[id]">
                    {{ label }}
                </UiMenuItem>
            </UiMenu>
            <button v-if="$user.canCreateEvent.value" class="md:order-2 form-button button-contained items-center" @click="createEvent">
                <IconPlus class="inline h-4 w-4" />
                Ereignis anlegen
            </button>
        </div>
        <EventFilterSummary @adjust-settings="openFilterSettings" v-model="filter" class="flex flex-none bg-white empty:hidden" />

        <UiLoader :is-loading="isFetching" v-if="currentView.value === 'list'">
            <div ref="listEl" class="flex flex-1 w-full transition-all duration-150 bg-white">
                <div v-if="!isFetching && !isSuccess" class="flex flex-col items-center flex-grow p-5 text-center text-softred-900 bg-softred-50">
                    <h3 class="text-lg">Ladehemmung</h3>
                    <p>Beim Anzeigen der Ereignisse läuft gerade etwas schief. Bitte lade die Seite neu oder melde dich einmal ab und wieder an.</p>
                </div>
                <div v-if="isSuccess && events.length > 0" class="flex-1 w-full">
                    <EventList :events="events" @eventsSlected="updateSelectedEvents" />
                </div>
                <div v-else-if="isSuccess" class="flex flex-col items-center flex-grow p-5 text-center text-softred-900 bg-softred-50">
                    <h3 class="text-lg">Keine Ereignisse vorhanden</h3>
                    <p>
                        Zu diesem Filter gibt es keine passenden Ereignisse. Du kannst den Filter anpassen, zum Beispiel den ein oder anderen Eintrag
                        weglassen oder den Zeitraum verändern. Vielleicht bringt dich das an dein Ziel.
                    </p>
                </div>
            </div>
        </UiLoader>

        <div
            v-if="currentView.value === 'list'"
            class="flex flex-col items-center justify-end flex-none mr-2 inset-x gap-x-2 gap-y-4 sm:mr-0 sm:flex-row sm:gap-y-0"
        >
            <template v-if="pageCount > 1">
                <UiPaginationSlider v-model="page" :page-count="pageCount" class="flex-1" />
            </template>
            <UiFormField label="Ereignisse pro Seite" :disabled="isFetching">
                <div class="flex gap-x-2">
                    <UiRadioInput v-model="pageSize" :value="10" label="10" />
                    <UiRadioInput v-model="pageSize" :value="20" label="20" />
                    <UiRadioInput v-model="pageSize" :value="50" label="50" />
                </div>
            </UiFormField>
        </div>

        <Calendar
            v-if="currentView.value === 'month' || currentView.value === 'week'"
            class="mt-8"
            :view="currentView.value"
            :filter="filter"
        />

        <EventCreateDialog :controller="dialog.createEvent" />
        <EventFilterComposer :controller="dialog.adjustFilterSettings" :filter="filter" :showTimePeriod="currentView.value === 'list'"/>
    </div>
</template>

<script setup lang="ts">

    definePageMeta({
        title: 'Ereignisse',
        middleware: ['requires-auth', 'redirects']
    })

    const router = useRouter()

    const filters = useEventFilters()

    type ViewOption = Option & { value: string };

    const viewOptions: ViewOption[] = [
        { label: 'Listenansicht', id: 0, value: 'list' },
        { label: 'Wochenansicht', id: 1, value: 'week'},
        { label: 'Monatsansicht', id: 2, value: 'month' },
    ]

    const currentView = ref<ViewOption>(viewOptions[0])

    const {
        today,
        days,
        weekDates,
        firstDayCurrentMonth,
        currentWeekNumber,
        weekdays,
        selectedDay,
        formattedCurrentMonth,
        formattedCurrentWeek,
        currentMonthDate,
        setWeek,
        formattedDateISO,
        formattedDateLabel,
        setMonth,
        selectDay,
    } = useCalendarDates()


    provide('calendar-dates', {
        today,
        days,
        weekDates,
        weekdays,
        currentWeekNumber,
        firstDayCurrentMonth,
        selectedDay,
        formattedCurrentMonth,
        formattedCurrentWeek,
        currentMonthDate,
        selectDay,
        setWeek,
        formattedDateISO,
        formattedDateLabel,
        setMonth,
    })

    const { events, filter, pageSize, isFetching, isSuccess, pageCount, page, refetch } = useEventList(filters)

    const listEl = ref<HTMLElement>()

    const dialog = {
        createEvent: useDialogController('createEvent'),
        adjustFilterSettings: useDialogController('adjustFilterSettings')
    }

    const { createEvent: commitCreatedEvent } = useCreateEvent()

    const runWithNotification = useRunWithNotification()

    async function createEvent() {

        const { data, isCanceled } = await dialog.createEvent.reveal()

        if (!isCanceled) {
            const event = await runWithNotification(() => commitCreatedEvent({data}), {
                pending: 'Das neue Ereignis wird gespeichert',
                error: 'Beim Speichern des Erignisses ist leider ein Fehler aufgetreten'
            })

            if (event instanceof Error) {
                return
            }

            router.push({ name: 'events-id-details', params: { id: event.id } })
        }
    }

    async function openFilterSettings() {
        const { data, isCanceled } = await dialog.adjustFilterSettings.reveal(filter.value)

        if (!isCanceled) {
            filter.value = data
        }
    }

    // Select multiple events (for the event action selector)
    const selectedEvents = ref([])
    const updateSelectedEvents = function (se) {
        selectedEvents.value = Object.keys(se).filter((id) => se[id])
    }
</script>
