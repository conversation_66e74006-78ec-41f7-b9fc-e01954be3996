// noinspection NpmUsedModulesInstalled

require('typescript-require');
const theme = require('tailwindcss/defaultTheme');
// import type { Config } from 'tailwindcss'
// import plugin from 'tailwindcss/plugin'
const plugin = require('tailwindcss/plugin')
const colors = require('./tailwind/colors')

// import colors from './tailwind/colors'
// import formsPlugin from './tailwind/forms-plugin'
const { plugin: formsPlugin } = require('./tailwind/forms-plugin')
const { plugin: iconsPlugin } = require('./tailwind/icons-plugin')

module.exports = {

    theme: {

        extend: {
            colors,

            fontFamily: {
                sans: ['Open Sans', 'Arial', 'Helvetica', 'sans-serif'],
            },
            boxShadow: {
                'shadow': '0px 1px 3px 0px rgba(54,54,52,0.1), 0px 0px 3px -1px rgba(0,0,0,0.15)',
                'shadow-md': '0px 4px 6px -1px rgba(54,54,52,0.1), 0px 0px 3px -1px rgba(0,0,0,0.15)',
                'shadow-lg': '0px 10px 15px -3px rgba(54,54,52,0.1), 0px 0px 3px -1px rgba(0,0,0,0.15)',
                'shadow-xl': '0px 20px 25px -6px rgba(54,54,52,0.1), 0px 0px 3px -1px rgba(0,0,0,0.15)',
                'shadow-2xl': '0px 30px 35px -12px rgba(54,54,52,0.1), 0px 0px 3px -1px rgba(0,0,0,0.15)'
            },
            minWidth: {
                '48': '192px',
                '12': '3rem'
            },
            maxWidth: {
                'xs': '12rem',
                '8xl': '88rem'
            },
            borderWidth: {
                '12': '12px'
            },
            gridTemplateColumns: {
                '24': 'repeat(24, minmax(0, 1fr))',
            }
        }
    },
    plugins: [
        require('@headlessui/tailwindcss'),
        iconsPlugin,
        formsPlugin,
        require("tailwindcss-animate"),
        plugin(function ({ addUtilities, addBase, theme }) {
            addBase({
                body: {
                    'font-smoothing': 'antialiased',
                    'color': theme('colors.grey.900'),
                    '-webkit-font-smoothing': 'antialiased'
                }
            })

            addUtilities({
                '.bg-image-check:checked': {
                    'background-image':
                        'url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGNsYXNzPSJoLTUgdy01IiB2aWV3Qm94PSIwIDAgMjAgMjAiIGZpbGw9IiNlNDY0NTAiPgogIDxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgZD0iTTE2LjcwNyA1LjI5M2ExIDEgMCAwMTAgMS40MTRsLTggOGExIDEgMCAwMS0xLjQxNCAwbC00LTRhMSAxIDAgMDExLjQxNC0xLjQxNEw4IDEyLjU4Nmw3LjI5My03LjI5M2ExIDEgMCAwMTEuNDE0IDB6IiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIC8+Cjwvc3ZnPg==")'
                },
                '.bg-image-check:checked:hover': {
                    'background-image':
                        'url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGNsYXNzPSJoLTUgdy01IiB2aWV3Qm94PSIwIDAgMjAgMjAiIGZpbGw9IiM4OTNjMzAiPgogIDxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgZD0iTTE2LjcwNyA1LjI5M2ExIDEgMCAwMTAgMS40MTRsLTggOGExIDEgMCAwMS0xLjQxNCAwbC00LTRhMSAxIDAgMDExLjQxNC0xLjQxNEw4IDEyLjU4Nmw3LjI5My03LjI5M2ExIDEgMCAwMTEuNDE0IDB6IiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIC8+Cjwvc3ZnPg==")'
                },
                '.bg-image-check:hover': {
                    'background-image':
                        'url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGNsYXNzPSJoLTUgdy01IiB2aWV3Qm94PSIwIDAgMjAgMjAiIGZpbGw9IiNlZmEyOTYiPgogIDxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgZD0iTTE2LjcwNyA1LjI5M2ExIDEgMCAwMTAgMS40MTRsLTggOGExIDEgMCAwMS0xLjQxNCAwbC00LTRhMSAxIDAgMDExLjQxNC0xLjQxNEw4IDEyLjU4Nmw3LjI5My03LjI5M2ExIDEgMCAwMTEuNDE0IDB6IiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIC8+Cjwvc3ZnPg==")'
                }
            })
        })
    ]
}
