import {
    differenceInMilliseconds, addDays, differenceInDays, startOfDay, parseISO,
    differenceInCalendarWeeks, startOfWeek, format, addWeeks, addHours,
    addMilliseconds, differenceInCalendarMonths, getDaysInMonth,
    startOfMonth, lastDayOfMonth, getDay, addMonths, setDay, setDate, isSameMonth,
    addMinutes
} from 'date-fns'
import { de } from 'date-fns/locale'

export const isSerialEvent = (event: Ref<ApiModel<'Event'>>) => {
    const _isSerialEvent = computed(() => {
        return event.value.eventTags.some(
            (tag: ApiModel<'CodeEntry'>) =>
                typeof tag.value2 === 'string' && /^SE-\d+$/.test(tag.value2)
        )
    })

    return _isSerialEvent
}

export const useCopyEvents = (events: Ref<ApiModel<'Event'>[]>, oryginalEvent: ApiModel<'Event'>) => {

    const originalEventDuration = useEventDuration(oryginalEvent)

    function addEvent() {
        const newEvent = useModelFactory('Event').create({
            ...oryginalEvent,
            number: null,
            id: Math.random(),
        })

        events.value = [...events.value, newEvent]
    }

    const hasEventToCopy = computed(() => {
        return events.value.length > 0
    })

    function removeEvent(id?: number) {
        events.value = events.value.filter(event => event.id !== id)
    }

    const getWeekDays = (date = new Date(), locale = de) => {
        const start = startOfWeek(date, { weekStartsOn: 1 })
        return Object.values(Array.from({ length: 7 }, (_, i) => ({
            id: i,
            label: format(addDays(start, i), 'EEEE', { locale })
        })))
    }

    function getNthWeekdayOfMonth(year: number, month: number, weekday: number, n: number | 'last') {
        const firstOfMonth = startOfMonth(new Date(year, month))

        if (n === 'last') {
            const lastOfMonth = lastDayOfMonth(firstOfMonth)
            const dayOfWeek = getDay(lastOfMonth)
            const diff = (dayOfWeek - weekday + 7) % 7
            return addDays(lastOfMonth, -diff)
        }

        const firstTarget = setDay(firstOfMonth, weekday, { weekStartsOn: 1 })
        const day = weekday === 0 ? 7 : weekday
        if (getDay(firstOfMonth) > day) {
            return addDays(firstTarget, 7 * n)
        }
        return addDays(firstTarget, 7 * (n - 1))
    }

    const weekDays = ref<{ id: number, label: string }[]>(getWeekDays())

    function getEventsByInterval(
        interval: number, options: {
            typeOfInterval: string, weekInterval: { days: number[] },
            monthInterval: { type: string, specificDayInMonth: number, everyDayInMonth: { type: string, day: { id: number, label: string } } },
        }, endDate: Date) {

        if (!endDate) {
            return
        }

        const oryginalStartDate = oryginalEvent.dateFrom
        const startHour = oryginalEvent.dateFrom.getHours()
        const startMinutes = oryginalEvent.dateFrom.getMinutes()
        const dateFrom = startOfDay(oryginalEvent.dateFrom)
        const dateUpTo = addHours(startOfDay(parseISO(endDate.toString())), startHour)
        const newEvents: ApiModel<'Event'>[] = []

        if (options.typeOfInterval === 'day') {
            const numberOfDays = differenceInDays(dateUpTo, dateFrom)
            for (let i = 1; i <= numberOfDays; i = i + interval) {
                const dateFrom = addDays(oryginalStartDate, i)
                const newEvent = useModelFactory('Event').create({
                    ...oryginalEvent,
                    number: null,
                    dateFrom: dateFrom,
                    dateUpTo: addMilliseconds(dateFrom, originalEventDuration),
                })
                newEvents.push(newEvent)
            }
            events.value = newEvents
        }

        if (options.typeOfInterval === 'week' && options.weekInterval.days.length > 0) {
            const numberOfWeeks = differenceInCalendarWeeks(dateUpTo, dateFrom, { weekStartsOn: 1 })
            const firstDayOfWeek = startOfWeek(oryginalEvent.dateFrom, { weekStartsOn: 1 })

            for (let i = 1; i <= numberOfWeeks; i = i + interval) {
                const week = addWeeks(firstDayOfWeek, i)
                for (let j = 0; j <= options.weekInterval.days.length - 1; j++) {

                    const dateFrom = addMinutes(addHours(addDays(week, options.weekInterval.days[j]), startHour), startMinutes)
                    if (dateFrom > dateUpTo) {
                        break;
                    }
                    const newEvent = useModelFactory('Event').create({
                        ...oryginalEvent,
                        number: null,
                        dateFrom: dateFrom,
                        dateUpTo: addMilliseconds(dateFrom, originalEventDuration),
                    })
                    newEvents.push(newEvent)
                }
            }
            events.value = newEvents
        }


        if (options.typeOfInterval === 'month' && options.monthInterval.type) {
            const numberOfMonths = differenceInCalendarMonths(dateUpTo, dateFrom)
            const type = options.monthInterval.type
            let dayOfTheMonthByNumber = options.monthInterval.specificDayInMonth
            const dayOfTheWeek = options.monthInterval.everyDayInMonth.day.id === 6 ? 0 : options.monthInterval.everyDayInMonth.day.id + 1
            const dayOfTheMonthType = options.monthInterval.everyDayInMonth.type

            for (let i = 1; i <= numberOfMonths; i = i + interval) {
                const currentMonth = addMonths(oryginalStartDate, i)
                const numberOfDaysInAMonth = getDaysInMonth(currentMonth)

                let startDate = null

                if (type === 'specificDay') {
                    if (dayOfTheMonthByNumber > numberOfDaysInAMonth) {
                        dayOfTheMonthByNumber = numberOfDaysInAMonth
                    }

                    startDate = setDate(addMonths(oryginalStartDate, i), dayOfTheMonthByNumber)
                }

                if (type === 'dayOfWeek') {
                    let whichRepeatedDayOfMonth: number | 'last' = 1
                    switch (dayOfTheMonthType) {
                        case 'first': {
                            whichRepeatedDayOfMonth = 1
                            break
                        }
                        case 'second': {
                            whichRepeatedDayOfMonth = 2
                            break
                        }
                        case 'third': {
                            whichRepeatedDayOfMonth = 3
                            break
                        }
                        case 'fourth': {
                            whichRepeatedDayOfMonth = 4
                            break
                        }
                        case 'last': {
                            whichRepeatedDayOfMonth = 'last'
                            break
                        }
                    }
                    startDate = addMinutes(addHours(getNthWeekdayOfMonth(currentMonth.getFullYear(), currentMonth.getMonth(), dayOfTheWeek, whichRepeatedDayOfMonth), startHour), startMinutes)

                }

                if (startDate <= dateUpTo) {
                    const newEvent = useModelFactory('Event').create({
                        ...oryginalEvent,
                        number: null,
                        dateFrom: startDate,
                        dateUpTo: addMilliseconds(startDate, originalEventDuration),
                    })
                    newEvents.push(newEvent)
                }
            }
            events.value = newEvents
        }
    }

    return {
        originalEventDuration,
        addEvent,
        removeEvent,
        getEventsByInterval,
        getWeekDays,
        weekDays,
        hasEventToCopy
    }
}

export const useEventDuration = (event: ApiModel<'Event'>) => {
    const originalEventDuration = differenceInMilliseconds(event.dateUpTo, event.dateFrom)
    return originalEventDuration
}
