<template>
    <ProvideEvent :event="event" v-slot="{ title, icon, description, linkedAssigment }">
        <div class="flex items-center justify-between w-full gap-x-2">
            <div class="flex items-center px-2 py-1 text-xs text-grey-900 bg-grey-100 gap-x-4 rounded-xl">
                <UiIcon :name="icon" class="flex-none w-6 h-auto" />
                {{ title }}
            </div>
            <EventActionsMenu :event="event" class="flex-none" />
        </div>

        <div class="flex flex-col gap-2 group/summary" :class="canManageData && 'hover:rounded-sm hover:bg-blue-50 hover:ring-4 hover:ring-blue-50'">
            <div class="flex justify-between">
                <h2 class="mt-2 text-lg truncate" :class="{ 'line-through': status.isCanceled }">{{ description }}</h2>
                <button
                    v-if="canManageData"
                    aria-label="Stammdaten bearbeiten"
                    type="button"
                    class="self-start xl:hidden xl:group-hover/summary:inline"
                    @click="action.editEvent">
                    <UiIcon aria-hidden="true" name="pencil-alt" class="w-6 h-6 text-blue-500" />
                </button>
            </div>
            <span v-if="linkedAssigment" class="-mb-2 text-sm empty:hidden">Verknüpfter Einsatz: {{ linkedAssigment }} </span>
            <span class="-mb-2 text-sm empty:hidden" :class="{ 'line-through': status.isCanceled }" v-text="event.organisation?.name" />
            <span class="text-sm" :class="{ 'line-through': status.isCanceled }">
                {{ $formatDateRangeWithAbbreviatedDayAndTime(event.dateFrom, event.dateUpTo) }}
            </span>
        </div>

        <hr v-if="_isSerialEvent" class="my-3 border-t-grey-100" />
        <div v-if="_isSerialEvent" class="flex flex-col gap-2">
            <h4 class="text-xs font-bold">Ereignis-Datum</h4>
            <UiListbox :disabled="isSeFutureSerialEventsLoading" placement="bottom-start" class="z-0">
                <template #button>
                    <div class="flex gap-1 items-center">
                         <IconRefresh v-if="isSeFutureSerialEventsLoading" class="w-8 h-auto animate-spin text-sky-500" />
                        <UiIcon v-else name="calendar-repeat" class="text-sky-500 w-8 h-auto" />
                        <span class="truncate text-sm font-normal">
                            {{ $formatDateRangeWithAbbreviatedDayAndTime(event.dateFrom, event.dateUpTo) }}
                        </span>
                    </div>
                </template>
                <template #default v-if="isSerialEventOptions">
                    <UiListboxOption  v-for="option in futureSerialEvents" :key="option.id" :value="option">
                        <NuxtLink :to="{ name: 'events-id-details', params: { id: option.id } }" class="text-xs text-sky-500  line-clamp-1 ">
                            {{ $formatDateRangeWithAbbreviatedDayAndTime(option.dateFrom, option.dateUpTo) }}
                        </NuxtLink>
                    </UiListboxOption>
                </template>
                <template #default v-else>
                    <UiListboxOption :disabled="true">
                        <span class="text-xs line-clamp-1">
                            Keine weiteren Ereignisse gefunden
                        </span>
                    </UiListboxOption>
                </template>
            </UiListbox>
        </div>

        <hr class="my-3 border-t-grey-100" />
        <div class="flex items-center gap-2">
            <h4 class="text-base font-semibold">Ereignisstatus</h4>
            <EventCancelStatus v-if="status.isCanceled" :event="event" />
        </div>

        <div class="flex gap-2 items-center ml-2">
            <span>Ist / Soll</span>
            <EventListItemStatus
                class="mb-2 flex cursor-pointer items-center justify-between rounded-md border-b border-gray-100 px-4 pb-2 transition duration-150 ease-in-out lg:mb-0 lg:block lg:border-b-0 lg:pb-1 lg:pl-1 lg:pr-1 lg:pt-1 lg:transition-all lg:hover:bg-blue-100"
                :status="status"
                :signUps="signUps"
                :event="event"
                :openComposer="false"
                :onClick="() => goToManageResources(event)"
            />
        </div>

        <div class="flex flex-col group/status" v-if="canCancel || canClose || canReactivate || canReopen">
            <EventStatusActionsMenu :event="event" />
            <NuxtLink :to="{ name: 'events-id-event-sharing', params: { id: event.id } }">
                <EventShareStatus
                    v-if="event.shareRequestReceived || event.shared"
                    :event="event"
                    :with-label="true"
                    class="px-1 ml-1 cursor-pointer w-fit hover:rounded-sm hover:bg-blue-50 hover:ring-4 hover:ring-blue-50" />
            </NuxtLink>
        </div>
        <div class="flex flex-col gap-1 px-1" v-else>
            <EventStatus :status="event.status" :with-label="true" />
            <EventShareStatus v-if="event.shareRequestReceived || event.shared" :event="event" :with-label="true" />
        </div>

        <hr class="my-3 border-t-grey-100" />

        <div v-if="canPublish" class="flex flex-col gap-2">
            <h4 class="text-base font-semibold">Eingeladen</h4>
            <p class="text-xs">Hier kannst du Menschen aus folgender Gliederung einladen:</p>
            <p class="text-xs bg-grey-100 py-0.5 px-2 rounded-full w-fit line-clamp-1 mb-2">{{ event.organisation.name }}</p>
            <EventInternalPublicationInvitations
                v-if="!isLoading"
                :event="event"
                :internal-publications="internalPublications"
                :is-clickable="true"
                :show-button="true" />
            <hr class="my-3 border-t-grey-100" />
        </div>

        <EventSharingSectionPanel :event="event" />

        <h4 class="text-base font-semibold">Meine Teilnahme</h4>

        <EventMySigningUpMenu :event="event" v-slot="{ status }">
            <EventSigningUpStatus :status="status" data-size="lg" class="text-sm text-gray-900" />
        </EventMySigningUpMenu>
    </ProvideEvent>
</template>

<script setup lang="ts">

    import { useEventStatus } from '~/composables/event'
    import { injectEventActions } from '~/composables/event-actions';
    import type { manageResourcesDialogType } from '~/composables/event-resources-dialog';
    import type { EventFilter } from '~~/composables/event-filter'
    const props = defineProps<{
        event: ApiModel<'Event'>
    }>()

    const status = computed(() => useEventStatus(props.event.status))
    const signUps = computed(() => useEventStatistics(props.event))

    const { canCancel, canClose, canManageData, canEdit, canReactivate, canReopen, canPublish } = inject(EventPermissionsKey)
    const { goToManageResources } = inject<manageResourcesDialogType>('resourcesDialog')

    const action  = injectEventActions()

    const { data: internalPublications, isLoading } = useEventQueries(props.event.id).internalPublication(canEdit.value)

    const _event = ref(props.event)
    const _isSerialEvent = isSerialEvent(_event)

    watch(() => props.event, () => {
        _event.value = props.event
    })

    const eventTag = computed<ApiModel<'CodeEntry'>[]>(() => {
        const tag = props.event.eventTags.find((tag: ApiModel<'CodeEntry'>) => tag.value2.startsWith('SE-'))
        return tag ? [tag] : []
    })

    const filterFutureEvents = ref<EventFilter>({
        start: props.event.dateFrom,
        limit: 11,
        eventTagSearchCriteria: {
            matchAll: true,
            eventTags: eventTag.value
        }
    })

    const { events: futureEvents, isLoading: isSeFutureSerialEventsLoading } = useEventList(filterFutureEvents)

    const isSerialEventOptions = computed(() => {
        return futureEvents.value.length > 1
    })

    const futureSerialEvents = computed<ApiModel<'Event'>[]>(() =>{
        return futureEvents.value.filter(event => event.id !== props.event.id)
    })

</script>
