<template>
    <UiComposer :is-revealed="isRevealed" @cancel="cancel" class="relative">
        <template #title>Extern Mitwirkende hinzufügen </template>
        <div class="mb-4 text-sm">
            <UiInfo message="Eine mündliche Vereinbarung oder ein schriftlicher Vertrag mit der externen Person genügen, um ihre Daten zu erheben. Grundlage ist Art. 6, Abs. 1b) der EU-DSGVO."/>
        </div>
        <div class="mb-4 flex flex-col gap-4">
            <UiFormField label="Vorname *" class="col-span-2">
                <input type="text" v-model="person.firstname" class="form-input box-border w-full" />
            </UiFormField>

            <UiFormField label="Nachname *" class="col-span-2">
                <input type="text" v-model="person.lastname" class="form-input box-border w-full" />
            </UiFormField>

            <UiFormField label="E-Mail-Adresse">
                <input type="text" v-model="v$.mail.$model" class="form-input box-border w-full" :class="{ 'border-red-500': v$.mail.$error }" />
            </UiFormField>

            <UiFormField label="Telefon">
                <input
                    type="text"
                    v-model="v$.telephone.$model"
                    class="form-input box-border w-full"
                    :class="{ 'border-red-500': v$.telephone.$error }" />
            </UiFormField>

            <UiIntervalInput
                label="Zeitraum *"
                class="col-span-2 w-full select-none"
                v-model:start="person.availableFrom"
                v-model:end="person.availableTo"
                :boundaries="{ start: event.dateFrom, end: event.dateUpTo }" />

            <UiFormField label="Anmerkung" class="col-span-2">
                <textarea type="text" class="form-textarea box-border w-full" rows="4" v-model="person.remark" />
            </UiFormField>

            <fieldset class="col-span-2 mt-4">
                <label>Qualifikationen</label>

                <UiFormField label="Einsatzqualifikationen">
                    <OperationQualificationSelect v-model="qualificationsFacade" />
                </UiFormField>

                <UiFormField label="Führerscheine" class="mt-3">
                    <input type="text" class="form-input box-border w-full" v-model="person.driverLicenses" />
                </UiFormField>
            </fieldset>
        </div>

        <template #footer>
            <div class="w-full flex flex-row flex-wrap sm:flex-nowrap justify-between items-center gap-2">
                <UiCheckInput class="basis-full mb-2 sm:mb-0 sm:basis-unset" label='weiteren Eintrag erstellen' v-model="createMultiple"/>
                <button @click="cancel" class="ml-auto form-button button-contained-secondary text-xs sm:text-base">Abbrechen</button>
                <button :disabled="v$.$invalid" class="form-button button-contained text-xs sm:text-base" @click="submit">Hinzufügen</button>
            </div>
        </template>
    </UiComposer>
</template>

<script lang="ts" setup>
    import { type DialogController } from '~~/composables/dialog-controller'
    import useVuelidate from '@vuelidate/core'
    import { required, email, helpers } from '@vuelidate/validators'
    import { ref } from 'vue'

    const props = defineProps<{
        controller: DialogController<'createExternalPerson'>
    }>()

    const createMultiple = ref(false)

    const { event } = inject(EventPlanningKey)

    const { isRevealed, onReveal, cancel, confirm } = props.controller

    const person = ref<ApiModel<'ExternalPerson'>>()

    onReveal(() => {
        person.value = useModelFactory('ExternalPerson').create({
            availableFrom: event.value.dateFrom,
            availableTo: event.value.dateUpTo
        })
    })

    const qualificationsFacade = computed({
        get() {
            return person.value.operationQualifications
        },
        set(value) {
            person.value.operationQualifications = value
        }
    })

    const validatePhoneNumber = (value: string) => {
        const tester = /^[+]*[(]{0,1}[0-9]{1,3}[)]{0,1}[-.\s/0-9]*$/g
        return !helpers.req(value) || tester.test(value)
    }

    const dateInRange = helpers.withMessage(
        'Start and end date needs to be in an event range.',
        (value: Date) => {
            return value <= event.value.dateUpTo && value >= event.value.dateFrom
        }
    )

    const validation = {
        firstname: { required },
        lastname: { required },
        mail: { email },
        telephone: { validatePhoneNumber },
        availableFrom: { required, dateInRange  },
        availableTo: { required, dateInRange  }
    }

    const v$ = useVuelidate<ApiModel<'ExternalPerson'>>(validation, person)

    function submit() {
        confirm({ person: person.value, createMultiple: createMultiple.value })
    }
</script>
