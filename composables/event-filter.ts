import type { Ref } from 'vue'
import { add, endOfMonth, startOfMonth } from 'date-fns'
import { DateTime } from 'luxon'

/**
 * Has two modes:
 *
 * In local storage or when transformed as parameters
 *
 * See `executedBy` for an example
 *
 */
export type EventFilter<T extends 'storage' | 'params' = 'storage'> = {
    /**
     * Filter "Zeitraum"
     */
    start?: Date

    end?: Date

    limit?: number

    /**
     * Filter "Art/Typ"
     */
    type?: 'ALL' | 'EVENT' | 'APPRENTICESHIP'

    /**
     * Bezeichnung
     */
    extendedDescription?: string

    /**
     * Tags
     */
    tags?: ApiModel<'CodeEntry'>[]

    /**
     * eventTagSearchCriteria
     */
    eventTagSearchCriteria?: {
        matchAll: boolean
        eventTags: ApiModel<'CodeEntry'>[]
    }

    /**
     * Nur meine aktiven Gliederungen
     */
    executedByMyOrganisations?: boolean

    /**
     * Nur Ereignisse folgender Gliederungen
     */
    executedBy?: T extends 'storage' ? { id: number; label: string }[] : number[]

    /**
     * Mit offenen Planstellen
     */
    withOpenEventPosts?: boolean

    /**
     * Mit ausstehender Rückmeldung (responseStatus = 'INVITED')
     */
    responseStatus?: 'ALL' | 'INVITED' | 'ANSWERED' | 'CONFIRMED' | 'DENIED'

    /**
     * Denen ich zugeordnet bin
     */
    assignedToEventPost?: boolean

    /**
     * Für die ich mich 'verfügbar' oder 'eingeschränkt verfügbar' gemeldet habe
     *
     * There is no way to use this value when searching via the Event API.
     * The filter is implemented on the client side.
     *
     * The condition is met if `event.mySigningUp?.isAvailabilityAnnounced === true`
     */
    // mySigningUp?: boolean

    /**
     * In denen ich einer Planstelle mit erweiterten Rechten zugeordnet bin
     */
    assignedToEventPostWithExtendedPermissions?: boolean

    /**
     * Für die ich als Manager oder Verantwortlicher eingetragen wurde
     */
    responsibleFor?: boolean

    /**
     * bei denen ich dem Dienstweg zustimmen muss
     */
    withDecisionsOfChainOfCommand?: boolean

    /**
     * Ereignisse, bei denen ich Einsatzzeiten von Helfer*innen erfassen dar
     */
    registratorFor?: boolean

    /**
     * Ereignisse status
     */

    status?: ('FINISHED' | 'CANCELED' | "APPROVED" | "WAITING_FOR_APPROVAL")[]

}

/**
 * Get or create the global filter state
 *
 * The state could also be fetched from the local storage at this point.
 * This would also be the place to set default values.
 */
export const useEventFilters = function (todayMinusDays = 7, todayPlusDays = 30) {
    const today = DateTime.now()
    return useState<EventFilter>('my-event-filters', () => ({
        start: today.minus({ days: todayMinusDays }).startOf('day').toJSDate(),
        end: today.plus({ days: todayPlusDays }).endOf('day').toJSDate(),
        type: "ALL"
    }))
}

/**
 * Use this as a proxy around the actual filter state
 *
 * Basically this is a collection of writable computed properties that handle and sanitize user inputs.
 *
 * @param filter
 */
export const useEventFilterFacade = function (filter: Ref<EventFilter>) {
    const type = computed<EventFilter['type']>({
        get: () => {
            return filter.value.type || 'ALL'
        },
        set: (value) => {
            filter.value.type = value
        }
    })

    const interval = computed<{ start: Date; end: Date }>({
        get: () => {
            return filter.value.start && filter.value.end
                ? {
                    start: filter.value.start,
                    end: filter.value.end
                }
                : { start: startOfMonth(add(new Date(), { months: -3 })), end: endOfMonth(add(new Date(), { months: 3 })) }
        },
        set: (value) => {
            if (value?.start && value?.end) {
                filter.value.start = value.start
                filter.value.end = value.end
            } else {
                filter.value.start = null
                filter.value.end = null
            }
        }
    })

    const withOpenEventPosts = computed<EventFilter['withOpenEventPosts']>({
        get: () => {
            return filter.value.withOpenEventPosts || false
        },
        set: (value) => {
            filter.value.withOpenEventPosts = value
        }
    })

    const responseStatus = computed<EventFilter['responseStatus']>({
        get: () => {
            return filter.value.responseStatus || 'ALL'
        },
        set: (value) => {
            filter.value.responseStatus = value
        }
    })

    const withExtendedPermissions = computed<EventFilter['assignedToEventPostWithExtendedPermissions']>({
        get: () => {
            return filter.value.assignedToEventPostWithExtendedPermissions || false
        },
        set: (value) => {
            filter.value.assignedToEventPostWithExtendedPermissions = value
        }
    })

    const responsibleFor = computed<EventFilter['responsibleFor']>({
        get: () => {
            return filter.value.responsibleFor || false
        },
        set: (value) => {
            filter.value.responsibleFor = value
        }
    })

    const assignedToEventPost = computed<EventFilter['assignedToEventPost']>({
        get: () => {
            return filter.value.assignedToEventPost || false
        },
        set: (value) => {
            filter.value.assignedToEventPost = value
        }
    })

    const inChainOfCommand = computed<EventFilter['withDecisionsOfChainOfCommand']>({
        get: () => {
            return filter.value.withDecisionsOfChainOfCommand || false
        },
        set: (value) => {
            filter.value.withDecisionsOfChainOfCommand = value
        }
    })

    const executedBy = computed<'MY' | 'ALL' | 'SELECTED'>({
        get: () => {
            if (!!filter.value.executedBy) {
                return 'SELECTED'
            }
            return !!filter.value.executedByMyOrganisations ? 'MY' : 'ALL'
        },
        set: (value) => {
            filter.value.executedByMyOrganisations = value === 'MY'
            filter.value.executedBy = value === 'SELECTED' ? filter.value.executedBy || [] : null
        }
    })

    const organisations = computed<EventFilter['executedBy']>({
        get: () => {
            return filter.value.executedBy
        },
        set: (value) => {
            filter.value.executedBy = value
        }
    })

    const extendedDescription = computed<EventFilter['extendedDescription']>({
        get: () => {
            return filter.value.extendedDescription || null
        },
        set: (value) => {
            filter.value.extendedDescription = value
        }
    })

    const registratorFor = computed<EventFilter['registratorFor']>({
        get: () => {
            return filter.value.registratorFor || false
        },
        set: (value) => {
            filter.value.registratorFor = value
        }
    })

    const status = computed<EventFilter['status']>({
        get: () => {
            return filter.value.status || []
        },
        set: (value) => {
            filter.value.status = value
        }
    })

    const tags = computed<EventFilter['tags']>({
        get: () => {
            return filter.value?.tags || []
        },
        set: (value) => {
            filter.value.tags = value
            if (value && value.length > 0) {
                filter.value.eventTagSearchCriteria = {
                    matchAll: true,
                    eventTags: value
                }
            } else {
                filter.value.eventTagSearchCriteria = null
            }
        }
    })

    const eventTagSearchCriteria = computed<EventFilter['eventTagSearchCriteria']>({
        get: () => {
            return filter.value?.eventTagSearchCriteria
        },
        set: (value) => {
            filter.value.eventTagSearchCriteria = value
        }
    })

    return {
        type,
        interval,
        withOpenEventPosts,
        responseStatus,
        withExtendedPermissions,
        responsibleFor,
        assignedToEventPost,
        inChainOfCommand,
        executedBy,
        organisations,
        extendedDescription,
        registratorFor,
        status,
        tags,
        eventTagSearchCriteria
    }
}
