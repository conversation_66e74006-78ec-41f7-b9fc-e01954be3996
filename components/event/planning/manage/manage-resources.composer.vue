<template>
    <div v-if="isRevealed" class="ml-auto h-full">
        <div class="relative flex h-full flex-col bg-white border border-grey-100 xl:p-5">
            <div class="flex flex-1 flex-col px-2 sm:px-6 pt-6">
                <TabGroup :selected-index="mode" @change="changeMode">
                    <TabList class="mb-8 flex">
                        <Tab class="tab">Personal</Tab>
                        <Tab class="tab">Material</Tab>
                    </TabList>
                    <TabPanels>
                        <TabPanel>
                            <EventPlanningManageStaffPanel as="div" @externePersoneEmit="createExternalPerson"/>
                        </TabPanel>
                        <TabPanel>
                            <EventPlanningManageMaterialPanel as="div" @openFavoriteMaterialDialog="manageFavoriteMaterials" @externeMaterialeEmit="createExternalMaterial"/>
                        </TabPanel>
                    </TabPanels>
                </TabGroup>
            </div>
        </div>

            <EventPlanningCreateExternalPersonDialog :controller="dialog.createExternalPerson"/>

            <EventPlanningCreateExternalMaterialDialog :controller="dialog.createExternalMaterial"/>

            <EventPlanningEditExternalMaterialDialog :controller="dialog.editExternalMaterial" as="bottom-sheet" />

            <EventPlanningSelectEventFavoriteMaterialsDialog :controller="dialog.manageFavoriteMaterials" as="bottom-sheet" />

            <EventPlanningCreateEventPostDialog :controller="dialog.createEventPost" />

            <EventPlanningSelectEventPostDialog :controller="dialog.selectEventPost" />

            <EventPlanningSigningUpPeriodsDialog :controller="dialog.selectPeriods" :event="event" as="bottom-sheet" />
   </div>
</template>

<script lang="ts" setup>
    import { TabGroup, TabList, Tab, TabPanels, TabPanel } from '@headlessui/vue'
    import SigningUpStatus from '~~/enums/event-signing-up-status'
    import { type UseConfirmDialogReturn } from '@vueuse/core'
import { is } from 'date-fns/locale';

    const props = defineProps<{
        controller: UseConfirmDialogReturn<null, null, null>
    }>()

    const { isRevealed, onReveal, cancel } = props.controller

    const { commit, event } = inject(EventPlanningKey)

    const dialog = {
        selectPeriods: useDialogController('selectSigningUpPeriods'),
        createExternalMaterial: useDialogController('createExternalMaterial'),
        createExternalPerson: useDialogController('createExternalPerson'),
        selectEventPost: useDialogController('selectEventPost'),
        createEventPost: useDialogController('createEventPost'),
        manageFavoriteMaterials: useDialogController('managaFavoriteMaterials'),
        editExternalMaterial: useDialogController('editExternalMaterial'),
    }

    /**
     * The available tabs
     */
    enum Mode {
        PERSONAL = 0,
        TECHNIC = 1
    }

    /**
     * We show the "Personal hinzufügen" tab by default
     */
    const mode = ref<Mode>(Mode.PERSONAL)

    /**
     * Control which tab is active
     */
    function changeMode(value: Mode) {
        mode.value = value
    }

    const runWithNotification = useRunWithNotification()

    const { push: notify } = useNotifications()

    const { createResourceSetting } = inject(EventPlanningActions)

    async function createExternalPerson() {
        const { data, isCanceled } = await dialog.createExternalPerson.reveal()

        if (!isCanceled) {
            if(data.createMultiple) {
                createExternalPerson()
            }

            createSigningUp(
                useModelFactory('Resource').create({
                    external: true,
                    type: 'PERSONAL',
                    externalPerson: data.person
                })
            )
        }
    }

    async function createExternalMaterial() {
        const { data: externalArticle, isCanceled } = await dialog.createExternalMaterial.reveal()

        if (!isCanceled) {
            createSigningUp(
                useModelFactory('Resource').create({
                    external: true,
                    type: 'TECHNIC',
                    externalArticle
                })
            )
        }
    }

    async function editExternalMaterial(signingUp: ApiModel<'SigningUp'>) {
        const { data: externalArticle, isCanceled } = await dialog.editExternalMaterial.reveal(signingUp)

        if (!isCanceled) {
            updateExternalMaterial(
                useModelFactory('Resource').create({
                    external: true,
                    type: 'TECHNIC',
                    externalArticle
                })
            )
        }
    }

    const { signingUpsByResourceType } = inject(EventPlanningKey)
    const signingUps = signingUpsByResourceType('TECHNIC')

    async function manageFavoriteMaterials() {
        const { data: selectedFavoritesMaterials, isCanceled } = await dialog.manageFavoriteMaterials.reveal()

        if (!isCanceled) {
            selectedFavoritesMaterials.filter(selectedFavorite => {
                return !signingUps.value.some(signUp => {
                    if (signUp.resource.external) {
                        return  selectedFavorite.technicArticle.id === signUp.resource.externalArticle.id
                    } else {
                        return  selectedFavorite.technicArticle.id === signUp.resource.article.id
                    }
                })
            }).map(({ technicArticle: article })  => {
                 createSigningUp(
                    useModelFactory('Resource').create({
                        external: false,
                        type: 'TECHNIC',
                        article
                    })
                )
            })
        }
    }

    async function selectEventPost(signingUp: ApiModel<'SigningUp'>) {
        const { data: eventPost, isCanceled } = await dialog.selectEventPost.reveal(signingUp)
        if (!isCanceled) {
            createResourceSetting(eventPost, signingUp)
        }
    }

    async function createEventPost(signingUp: ApiModel<'SigningUp'>) {
        const { data: eventPost, isCanceled } = await dialog.createEventPost.reveal(signingUp)

        if (!isCanceled) {
            const createdEventPost = await runWithNotification(() => commit('createEventPost', eventPost), {
                pending: `Die Planstelle "${eventPost.description} wird erstellt.`,
                error: `Du wolltest die Planstelle "${eventPost.description}" erstellem. Das hat nicht geklappt.`
            })

            if (createdEventPost instanceof Error) {
                return null
            }

            return createResourceSetting(createdEventPost, signingUp)
        }
    }

    const { createSigningUpModel, updateSigningUpModel } = useSigningUpStates()

    async function createSigningUp(resource: ApiModel<'Resource'>) {
        await commit('createSigningUp', createSigningUpModel(resource))
    }

    async function updateExternalMaterial(resource: ApiModel<'Resource'>) {
        await commit('updateExternalMaterial', createSigningUpModel(resource))
    }

    async function removeSigningUp(signingUp: ApiModel<'SigningUp'>) {
        try {
           await commit('removeSigningUp', signingUp)
        } catch (error) {
            if(error.data.statusCode === 404) {
                notify({
                    type: 'error',
                    content:
                    'Du wolltest einen Eintrag aus der Ressourcenliste entfernen. Das hat nicht geklappt.'
                })
            }
            if(error.data.statusCode === 400 && error.data.data[0].field === "eventExternalPersonal") {
                notify({
                    type: 'error',
                    content:
                    'Du wolltest die Ressource aus der Liste entfernen. Das hat nicht geklappt. Bei extern Mitwirkenden, entferne zunächst alle Registrierungen.'
                })
            }
        }

    }

    async function changeSigningUpStatus(signingUp: ApiModel<'SigningUp'>, status: SigningUpStatus) {
        switch (status) {
            case SigningUpStatus.UNAVAILABLE:
            case SigningUpStatus.AVAILABLE: {
                await commit('updateSigningUp', updateSigningUpModel(signingUp, status))
                break
            }

            case SigningUpStatus.PARTIAL: {
                const { data: periods, isCanceled } = await dialog.selectPeriods.reveal(signingUp)

                if (!isCanceled) {
                    await commit('updateSigningUp', updateSigningUpModel(signingUp, status, periods))
                }
                break
            }
        }
    }

    provide(EventManageResourcesActions, {
        selectEventPost,
        createEventPost,
        removeSigningUp,
        createSigningUp,
        changeSigningUpStatus,
        editExternalMaterial,
    })
</script>

<style scoped lang="pcss">
    .tab {
        @apply px-4 w-1/2 py-2 text-center !mr-1 text-sm font-normal border-b border-b-sky-200 text-sky-500 whitespace-nowrap hover:border-b-sky-500;
        @apply ui-selected:font-medium ui-selected:border-b-2 ui-selected:border-b-sky-500
    }
</style>
