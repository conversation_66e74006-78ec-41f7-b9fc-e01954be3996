import { useConfirmDialog, type UseConfirmDialogReturn } from '@vueuse/core'
import { ApiSchema } from '~~/schema/factory'
import type { EventFilter } from '~~/composables/event-filter'
import type { CopyEventData } from '../components/event/copy.dialog.vue'

type CONTROLLERS = {
    /**
     * Utility to control the Dialog "Create External Material"
     */
    createExternalMaterial: UseConfirmDialogReturn<null, ApiModel<'ExternalTechnicArticle'>, null>

    /**
     * Utility to control the Dialog "Create External Person"
     */
    createExternalPerson: UseConfirmDialogReturn<null, { person: ApiModel<'ExternalPerson'>, createMultiple: boolean }, null>

    /**
     * Utility to control the Dialog "Create Event Posts"
     */
    createMultipleEventPosts: UseConfirmDialogReturn<boolean, { eventPosts: ApiModel<'EventPost'>[], createMultiple: boolean }, null>

    /**
     * Utility to control the Dialog "Create event post for the given signing up"
     */
    createEventPost: UseConfirmDialogReturn<ApiModel<'SigningUp'>, ApiModel<'EventPost'>, null>

    /**
     * Open the maurer scheme input in order to calculate multiple event posts
     */
    applyMaurerScheme: UseConfirmDialogReturn<boolean, { eventPosts: ApiModel<'EventPost'>[], createMultiple: boolean }, boolean>

    /**
     * Utility to control the Dialog "Edit event post"
     */
    editEventPost: UseConfirmDialogReturn<{ eventPost: ApiModel<'EventPost'>, event: ApiModel<'Event'> }, ApiModel<'EventPost'>, null>

    /**
     * Utility to control the Dialog "Select an event post for a given signing up"
     */
    selectEventPost: UseConfirmDialogReturn<ApiModel<'SigningUp'>, ApiModel<'EventPost'>, null>

    /**
     * Utility to control the Dialog "Select a signing up for the given event post"
     */
    selectSigningUp: UseConfirmDialogReturn<ApiModel<'EventPost'>, ApiModel<'SigningUp'>, null>

    /**
     * Utility to control the dialog "Select Periods"
     */
    selectSigningUpPeriods: UseConfirmDialogReturn<ApiModel<'SigningUp'>, ApiModel<'Period'>[], null>

    /**
     * Utility to control the dialog "Change Periods of a Resource Setting"
     */
    editResourceSetting: UseConfirmDialogReturn<ApiModel<'ResourceSetting'>, Interval[], null>

    /**
     * Utility to control the dialog "Change Periods of a Resource Setting"
     */
    createPrintableReport: UseConfirmDialogReturn<ApiModel<'Event'>[], null, null>

    /**
     * Utility to make sure there is no data flow
     */
    emptyDialog: UseConfirmDialogReturn<null, null, null>

    /**
     * Utility to control the confirmation dialog
     */
    confirmation: UseConfirmDialogReturn<null, null, null>

    /**
     * This is the dialog that opens in "Registrierungen" when user wants to change start/end for multiple registrations
     */
    updateMultipleRegistrations: UseConfirmDialogReturn<ApiModel<'EventRegistration'>[], ApiModel<'EventRegistration'>[], null>

    /**
     * Dialog gathering base data for an event and its intitial publication state
     */
    createEvent: UseConfirmDialogReturn<null, ApiModel<'Event'>, null>

    /**
     * Dialog allowing users to edit an events base data like its type and descriptions
     */
    editEvent: UseConfirmDialogReturn<null, ApiModel<'Event'>, null>

    /**
     * Dialog allowing users to copy an events
     */
    copyEvent: UseConfirmDialogReturn<ApiModel<'Event'>, CopyEventData, null>

    /**
     * Dialog that opens before closing an event
     */
    closeEvent: UseConfirmDialogReturn<null, ApiModel<'MemberData'>[], any>

    /**
     * Dialog that asks for reasons before an event is cancelled.
     */

    cancelEvent: UseConfirmDialogReturn<any, ApiSchema<'EventCancelReason'>, any>


    /**
     * This dialog lets users edit metadata of documents
     */
    editEventDocument: UseConfirmDialogReturn<any, ApiModel<'EventDocumentMeta'>, null>


    /**
     * This dialog lets users edit metadata of documents
     */
    adjustFilterSettings: UseConfirmDialogReturn<EventFilter, EventFilter, null>

    /**
     * This dialog lets users sign up favorite materials
     */
    managaFavoriteMaterials: UseConfirmDialogReturn<null, ApiModel<'FavoriteTechnicArticle'>[], null>

    /**
     * This dialog lets users edit external material
     */
    editExternalMaterial: UseConfirmDialogReturn<ApiModel<'SigningUp'>, ApiModel<'ExternalTechnicArticle'>, null>

    /**
     * This dialog lets users to have a quic view on resources for event post
     */
    viewResources: UseConfirmDialogReturn<ApiModel<'SigningUp'>, null, null>

    /**
    * This dialog lets users to register external person in registration tab
    */
    registerExternalPerson: UseConfirmDialogReturn<
        ApiModel<'RegistrationCard'>,
        { person: ApiModel<'RegistrationCard'>; start: Date | null; end: Date | null },
        null
    >

    /**
     * This dialog lets users to to see member personal data
     */
    viewMemberData: UseConfirmDialogReturn<ApiModel<'MemberData'> | ApiModel<'ExternalPerson'>, null, null>

    /**
     * This dialog lets users to to see member personal data
     */
    viewEventPostItem: UseConfirmDialogReturn<number, null, null>

    /**
    * This dialog helps user to invite other organisations via internal publications
    */
    internalPublicationInviteHelp: UseConfirmDialogReturn<ApiModel<'InternalPublication'>, ApiModel<'InternalPublication'>, null>

}

type DialogName = keyof CONTROLLERS

export type DialogController<T extends DialogName> = CONTROLLERS[T]

/**
 * This is a simple wrapper for `useConfirmDialog`
 *
 * @param dialog
 */
export function useDialogController<T extends DialogName>(dialog: T | T[]) {
    return useConfirmDialog() as DialogController<T>
}
