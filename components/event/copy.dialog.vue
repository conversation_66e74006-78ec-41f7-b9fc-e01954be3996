<template>
    <UiComposer :is-revealed="isRevealed" @cancel="cancel">
        <template #title>{{ title }} {{`(${eventsToCopyCount})`}} </template>

        <template v-if="isRevealed">
            <div class="flex flex-col gap-4 mb-8">
                <UiDisclosure v-model:open="open" class="mb-8">
                    <template #button>
                        <span class="flex items-center gap-4 text-base"> <UiIcon name="information-circle" class="h-5 w-5" /> So geht das </span>
                        <UiIcon
                        name="chevron-down"
                        class="text-gray-900transform h-6 w-6 transition-all"
                        :class="{ 'rotate-180': open, 'rotate-0': !open }" />
                    </template>
                    <div class="flex flex-col gap-4 text-xs">
                        <p>
                            Der drkserver kopiert die Ereignis-Infos ohne Dokumente, Ereignisnummer und Kennzeichen.
                        </p>
                        <p>
                            <PERSON><PERSON><PERSON> kannst du zus<PERSON>, je nachdem was im Ereignis vorhanden ist:
                        </p>
                        <ul class="list-disc list-outside pl-6">
                            <li>Eingeladene aus deiner Gliederung</li>
                            <li>Ereignisverantwortliche</li>
                            <li>Planstellen</li>
                            <li>Personen aus "Wer darf registrieren?"</li>
                        </ul>
                        <p>
                            Beachte: Wenn du schon Personen eingeladen hast und dann kopierst, sind alle neuen Ereignisse
                            sofort für diese Personen sichtbar. Für die neuen Ereignisse werden dann auch
                            Push-Benachrichtigungen an die drkserver-App der Helfenden versendet.
                        </p>
                        <p>
                            Beim Kopieren des neuen Ereignisses gibst du nur Datum und Beginn an. Ein Ereignis, das du kopierst,
                            dauert genauso lange wie das Original. Du kannst die Dauer nach dem Kopieren ändern.
                        </p>
                    </div>
                </UiDisclosure>
                <p class="font-bold text-lg">
                    <template v-if="canCopyResponsiblePersons || canCopyRegistrators || canCopyEventPosts || canCopyInvitations">
                        Kopiere die Ereignis-Infos und
                    </template>
                    <template v-else>
                        Es werden nur die Ereignis-Infos kopiert, denn
                    </template>
                </p>
                <UiLoader :is-loading="isLoading" class="">
                    <fieldset class="mb-4 flex flex-col gap-y-4">
                        <UiCheckInput label="Alle Eingeladenen aus deiner Gliederung" v-model="copyData.canCopyInvitations" v-if="canCopyInvitations"/>
                        <UiCheckInput label="Ereignisverantwortliche" v-model="copyData.canCopyResponsiblePersons" v-if="canCopyResponsiblePersons"/>
                        <UiCheckInput label="Alle Planstellen" v-model="copyData.canCopyEventPosts" v-if="canCopyEventPosts" />
                        <UiCheckInput label="Personen aus “Wer darf registrieren?“" v-model="copyData.canCopyRegistrators" v-if="canCopyRegistrators" />
                    </fieldset>
                    <ul v-if="!canCopyResponsiblePersons || !canCopyRegistrators || !canCopyEventPosts || !canCopyInvitations"
                        class="mb-4 list-disc list-outside pl-6 space-y-1">
                        <li v-if="!canCopyInvitations">Eingeladen: niemand eingeladen</li>
                        <li v-if="!canCopyResponsiblePersons">Ereignisverantwortliche: niemand eingetragen außer dir</li>
                        <li v-if="!canCopyEventPosts">Planstellen: keine angelegt</li>
                        <li v-if="!canCopyRegistrators">"Wer darf registrieren?": niemand eingetragen</li>
                    </ul>
                </UiLoader>

                <TabGroup :selected-index="mode" @change="changeMode">
                    <TabList class="mb-8 flex">
                        <Tab class="tab">Ereignis kopieren</Tab>
                        <Tab class="tab">Serie erstellen</Tab>
                    </TabList>
                    <TabPanels>
                        <TabPanel>
                            <EventCopy v-model="copyEvents" :event="event" :originalEventDuration="originalEventDuration" />
                        </TabPanel>
                        <TabPanel>
                            <EventCopySeries
                                v-model="copyEventsBySerie"
                                :event="event"
                                @update:validateSeries="validateSeries"
                            />
                        </TabPanel>
                    </TabPanels>
                </TabGroup>
            </div>
        </template>

        <template #footer>
            <div class="flex flex-col gap-4">
                <div class="w-full flex justify-end">
                    <button class="form-button button-contained-secondary" @click="cancel">Abbrechen</button>
                    <button class="form-button button-contained" @click="submit" :disabled="validator.$invalid">{{ title }}</button>
                </div>
            </div>
        </template>
    </UiComposer>
</template>

<script lang="ts" setup>
    import { DialogController } from '~~/composables/dialog-controller'
    import { addMilliseconds } from 'date-fns'
    import useVuelidate from '@vuelidate/core'
    import { required, helpers } from '@vuelidate/validators'
    import { TabGroup, TabList, Tab, TabPanels, TabPanel } from '@headlessui/vue'

    export type CopyEventData = {
        dresscodes: ApiModel<'CodeEntry'>[],
        caterings: ApiModel<'CodeEntry'>[],
        locations: ApiModel<'EventAddress'>[],
        organizer: ApiModel<'ComplexAddressContact'>
        resourceSettings: ApiModel<'ResourceSetting'>[],
        responsiblePersons: ApiModel<'MemberData'>[],
        canCopyResponsiblePersons: boolean,
        registrators: ApiModel<'MemberData'>[],
        canCopyRegistrators: boolean,
        eventPost: ApiModel<'EventPost'>[],
        canCopyEventPosts: boolean,
        signingUps: ApiModel<'SigningUp'>[],
        eventsToCopy: ApiModel<'Event'>[],
        event: ApiModel<'Event'>,
        canCopyInvitations: boolean,
        internalPublications: ApiModel<'InternalPublication'>[]
        mode: Mode
    }

    const props = defineProps<{
        event: ApiModel<'Event'>
        controller: DialogController<'copyEvent'>
    }>()

    const { isRevealed, confirm, cancel } = props.controller

    enum Mode {
        COPY = 0,
        SERIES = 1
    }

    const mode = ref<Mode>(Mode.COPY)

    const title = computed(()=>{
        return mode.value === Mode.COPY ? 'Ereignis kopieren' : 'Serie erstellen'
    })

    function changeMode(value: Mode) {
        mode.value = value
        copyData.value.mode = value
    }

    const copyData = ref<CopyEventData>({
        dresscodes: [],
        caterings: [],
        locations: [],
        organizer: null,
        responsiblePersons: [],
        resourceSettings: [],
        canCopyResponsiblePersons: false,
        registrators: [],
        canCopyRegistrators: false,
        eventPost: [],
        canCopyEventPosts: false,
        canCopyInvitations: false,
        signingUps: [],
        internalPublications: [],
        eventsToCopy: [],
        event: props.event,
        mode: Mode.COPY
    })

    const copyEvents = ref<ApiModel<'Event'>[]>(
       [useModelFactory('Event').create({
            ...props.event,
            number: null
        })]
    )

    const copyEventsBySerie = ref<ApiModel<'Event'>[]>([props.event])

    const eventsToCopyCount = computed(()=>{
        return mode.value === Mode.COPY ? copyEvents.value.length : copyEventsBySerie.value.length
    })

    const originalEventDuration = useEventDuration(props.event)
    const { canEdit } = inject(EventPermissionsKey)
    const { data: internalPublications, isLoading: isLoadingInternalPublications } = useEventQueries(props.event.id).internalPublication(canEdit.value)

    const events = computed(()=>{
        return mode.value === Mode.COPY ? copyEvents.value : copyEventsBySerie.value
    })

    const isSerieValid = ref<boolean>(false)

    const validateSeries = (isValid: boolean) => {
        return isSerieValid.value = isValid
    }

    const isSerieValidated = helpers.withMessage('Series validation failed.', () => {
        return mode.value === Mode.SERIES ? isSerieValid.value : true
    })

    const rules = computed(() => {
        return {
            events: {
                $each: helpers.forEach({
                    dateFrom: { required }
                })
            },
            validateSeries: { isSerieValidated  }
        }
    })

    const validator = useVuelidate(rules, { events: events, validateSeries: isSerieValidated })

    const open = ref<boolean>(false)
    const { $user } = useNuxtApp()

    const { data: responsiblesData } = useEventQuery('responsibles')
    const { data: registratorsData, isLoading: isLoadingRegistratorsData } = useEventQuery('registrators')
    const { data: eventPostsData } = useEventQuery('eventPosts')
    const { data: signingUpsData, isLoading: isLoadingSigningUpsData  } = useEventQuery('signingUps')
    const { data: cateringsData } = useEventQuery('caterings')
    const { data: dresscodesData } = useEventQuery('dresscodes')
    const { data: locationsData } = useEventQuery('locations')
    const { data: organizerData } = useEventQuery('organizer')
    const { data: resourceSettingsData } = useEventQuery('resourceSettings')

    const responsibles = computed<ApiModel<'MemberData'>[]>(() => {
       return responsiblesData.value ?? props.event.responsibles.map(person => person.masterdata)
    })

    const eventPosts = computed(() => {
        return eventPostsData.value ?? props.event.eventPosts
    })

    const registrators = computed<ApiModel<'MemberData'>[]>(()=>{
        return registratorsData.value ?? []
    })

    const signingUps = computed<ApiModel<'SigningUp'>[]>(()=>{
        return signingUpsData.value ?? props.event.allSigningUps?.items
    })

    const dresscodes = computed<ApiModel<'CodeEntry'>[]>(()=>{
        return dresscodesData.value ?? props.event.dressCodes
    })

    const caterings = computed<ApiModel<'CodeEntry'>[]>(()=>{
        return cateringsData.value ?? props.event.caterings
    })

    const locations = computed<ApiModel<'EventAddress'>[]>(()=>{
        return locationsData.value ?? props.event.locations
    })

    const organizer = computed<ApiModel<'ComplexAddressContact'>>(()=>{
        return organizerData.value ?? props.event.organizer
    })

    const resourceSettings = computed<ApiModel<'ResourceSetting'>[]>(()=>{
        return resourceSettingsData.value ?? []
    })

    const isLoading = computed<boolean>(()=> isLoadingRegistratorsData.value || isLoadingSigningUpsData.value || isLoadingInternalPublications.value)

    const canCopyResponsiblePersons = computed<boolean>(()=>{
        if(responsibles.value.length === 0) return false
        if(responsibles.value.length > 1) return true
        if(responsibles.value[0].id == $user.basicData.id) return false
        return true
    })

    const canCopyRegistrators = computed<boolean>(()=>{
       return registrators.value.length > 0
    })

    const canCopyEventPosts = computed<boolean>(()=>{
       return eventPosts.value.length > 0
    })

    const canCopyInvitations = computed<boolean>(()=>{
       return internalPublications.value?.length > 0
    })

    function submit() {
        copyData.value.eventPost = eventPosts.value
        copyData.value.dresscodes = dresscodes.value
        copyData.value.caterings = caterings.value
        copyData.value.registrators = registrators.value
        copyData.value.signingUps = signingUps.value
        copyData.value.resourceSettings = resourceSettings.value
        copyData.value.locations = locations.value
        copyData.value.organizer = organizer.value
        copyData.value.internalPublications = internalPublications.value
        copyData.value.responsiblePersons = responsibles.value.filter(person => person.id !== $user.basicData.id)
        if(mode.value === Mode.SERIES) {
            copyData.value.eventsToCopy = copyEventsBySerie.value.map((event) => {
                event.extendedDescription = `${event.extendedDescription}`
                return event
            })
        } else {
            copyData.value.eventsToCopy = copyEvents.value.map((event) => {
                event.dateUpTo = addMilliseconds(event.dateFrom, originalEventDuration)
                event.extendedDescription = `${event.extendedDescription}`
                return event
            })
        }

        confirm(copyData.value)
    }
</script>

<style scoped lang="pcss">
    .tab {
        @apply px-4 w-1/2 py-2 text-center !mr-1 text-sm font-normal border-b border-b-sky-200 text-sky-500 whitespace-nowrap hover:border-b-sky-500;
        @apply ui-selected:font-medium ui-selected:border-b-2 ui-selected:border-b-sky-500
    }
</style>
