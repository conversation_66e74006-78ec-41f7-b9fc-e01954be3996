<template>
    <div class="flex flex-col gap-8">
        <p class="font-bold text-lg">Ereignis als Serie anlegen</p>
        <div class="flex flex-wrap items-center gap-x-4">
            <UiIntegerInput top-label="alle" v-model="count" :min="1" :max="99" @click.prevent />
            <UiListbox v-model="timePeriod" class="w-48">
                <template #button>{{ timePeriod?.label || 'Modul auswählen' }}</template>
                <UiListboxOption v-for="option in timePeriodOptions" :key="option.id" :value="option">
                    {{ option.label }}
                </UiListboxOption>
            </UiListbox>

            <fieldset v-if="timePeriod.id === 'week'" class="flex flex-wrap gap-2 mt-6">
                <button
                    type="button"
                    v-for=" ({id, label}) in weekDaysOptions"
                    :key="label"
                    @click="getDayInterval(id)"
                    class="w-8 h-8 md:w-10 md:h-10 flex items-center justify-center rounded-full bg-gray-300 cursor-pointer hover:bg-sky-300 hover:text-white"
                    :class="{ 'bg-sky-700 text-white': isSelected(id) }"
                >
                    {{ label.charAt(0).toUpperCase() }}
                </button>
                <div v-if="isIntervalSelected"  class="flex mt-4">
                    <span>Findet wöchentlich von {{ startDayName }} bis {{ endDayName }} statt.</span>
                </div>
            </fieldset>

            <UiFormField v-if="timePeriod.id === 'month'" class="flex flex-col gap-3">
                <UiRadioInput label="an Tag" v-model="dayOrSerie" value="specificDay" class="items-center gap-4">
                    <UiIntegerInput v-model="dayOfMonth" :min="1" :max="31" @click.prevent :withButtons="false" class="my-0" />
                </UiRadioInput>
                <UiRadioInput label="am" v-model="dayOrSerie" value="dayOfWeek" class="items-center gap-4">
                    <div class="flex flex-wrap gap-4">
                        <UiListbox v-model="weekPeriod" class="w-48">
                            <template #button>{{ weekPeriod?.label || 'Modul auswählen' }}</template>
                            <UiListboxOption v-for="option in periodOfWeeksInMonth" :key="option.id" :value="option">
                                {{ option.label }}
                            </UiListboxOption>
                        </UiListbox>
                        <UiListbox v-model="dayOfWeek" class="w-48">
                            <template #button>{{ dayOfWeek.label || 'Modul auswählen' }}</template>
                            <UiListboxOption v-for="(weekDay) in weekDaysOptions" :key="weekDay.label" :value="weekDay">
                                {{ weekDay.label }}
                            </UiListboxOption>
                        </UiListbox>
                    </div>
                </UiRadioInput>
            </UiFormField>
        </div>

        <UiFormField label="Serie endet am"  class="">
            <UiDatePicker v-model="endDate" :min="minDate"/>
        </UiFormField>
    </div>


</template>

<script lang="ts" setup>
    import { isBefore, parseISO, startOfDay, startOfMonth, getDaysInMonth, startOfWeek, addWeeks, addMonths, addDays } from 'date-fns'
    import { useVModel } from '@vueuse/core'

    const props = defineProps<{
        modelValue: ApiModel<'Event'>[],
        event: ApiModel<'Event'>
    }>()

    const emit = defineEmits<{
        (e: 'update:modelValue', value: ApiModel<'Event'>[]): void,
        (e: 'update:validateSeries', value: boolean): void
    }>()

    const copyEvents = useVModel(props, 'modelValue', emit)

    const count = ref<number>(1)

    type timePeriodOption = {
        id: string,
        label: string
    }

    const timePeriodOptions = ref<timePeriodOption[]>([
        { id: 'day', label: 'Tage'},
        { id: 'week', label: 'Wochen'},
        { id: 'month', label: 'Monate'}
    ])
    const timePeriod = ref<timePeriodOption>(timePeriodOptions.value[0])

    const startDay = ref<number>(null)
    const endDay = ref<number>(null)
    const minDate = ref<Date>(addDays(props.event.dateFrom, 1))

    const startDayName = computed(() => {
        return weekDays.value[startDay.value]?.label
    })

    const endDayName = computed(() => {
        return weekDays.value[endDay.value]?.label
    })

    const days = ref<number[]>([])

    const getDayInterval = (index: number) => {
        if(days.value.includes(index)) {
            days.value = days.value.filter(day => day !== index)
        } else {
            days.value = [...days.value, index]
        }
    }

    const isSelected = (id: number) => {
        return days.value.includes(id)
    }

    const isIntervalSelected = computed(() => {
        return startDay.value !== null && endDay.value !== null && startDay.value !== endDay.value
    })

    const dayOrSerie = ref<string>(null)

    const periodOfWeeksInMonth = ref<timePeriodOption[]>([
        { id: 'first', label: 'ersten'},
        { id: 'second', label: 'zweiten'},
        { id: 'third', label: 'dritten'},
        { id: 'fourth', label: 'vierten'},
        { id: 'last', label: 'letzten'}
    ])

    const { getEventsByInterval, weekDays, hasEventToCopy } = useCopyEvents(copyEvents, props.event)

    const weekDaysOptions = computed(() => {
        const tempArray = [...weekDays.value]
        return tempArray
    })

    const weekPeriod = ref<timePeriodOption>(periodOfWeeksInMonth.value[0])
    const endDate = ref<Date>(null)
    const dayOfWeek = ref<{id: number, label: string }>(weekDaysOptions.value[0])
    const dayOfMonth = ref<number>(1)

    watch(endDate, (newDate) => {
        if (!newDate) return

        const numberOfDaysInAMonth = getDaysInMonth(startOfMonth(parseISO(newDate.toString())))

        if (dayOfMonth.value > numberOfDaysInAMonth) {
            dayOfMonth.value = numberOfDaysInAMonth
        }
    })

    watch([count, endDate, timePeriod, startDay, endDay, dayOrSerie, weekPeriod, dayOfWeek, days, dayOfMonth], () => {

        switch (timePeriod.value.id) {
            case 'day':
                minDate.value = addDays(props.event.dateFrom, 1)
                break;
            case 'week':
                minDate.value = addWeeks(startOfWeek(props.event.dateFrom, { weekStartsOn: 1 }), 1)
                break;
            case 'month':
                minDate.value = addMonths(startOfMonth(props.event.dateFrom), 1)
                break;
        }

        getEventsByInterval(
            count.value,
            {
                typeOfInterval: timePeriod.value.id,
                weekInterval: {
                   days: days.value
                },
                monthInterval: {
                    type: dayOrSerie.value,
                    specificDayInMonth: dayOfMonth.value,
                    everyDayInMonth: {
                        type: weekPeriod.value.id,
                        day :dayOfWeek.value
                    }
                }
            },
            endDate.value)

    },{
        immediate: true
    })

    const isSeriesValid = computed<boolean>(() => {
        if (!endDate.value || isBefore(startOfDay(parseISO(endDate.value.toString())), startOfDay(props.event.dateFrom))) return false
        if (!hasEventToCopy.value) return false

        switch (timePeriod.value.id) {
            case 'day':
                return true
            case 'week':
                return days.value.length > 0
            case 'month':
                return !!dayOrSerie.value
            default:
                return false
        }
    })

    watch(isSeriesValid, (newVal) => {
        emit('update:validateSeries', newVal)
    })

</script>
