<template>
    <div
        v-if="oneOrganisationInvited"
        @[isClickable&&`click`]="action.internalPublicationShowInvitations()"
        class="flex items-center gap-2 "
        :class="{ 'hover:cursor-pointer hover:rounded-sm hover:bg-blue-50 hover:ring-4 hover:ring-blue-50': isClickable }">
        <UiIcon name="published" class="h-6 w-6 text-blue-500" />
        <span class="line-clamp-2 w-full text-sm group-[.flex]:line-clamp-5">{{ oneOrganisationInvitationDescription }}</span>
    </div>
    <div
        v-else-if="allOrganisationsInvited"
        @[isClickable&&`click`]="action.internalPublicationShowInvitations()"
        class="flex items-center gap-2 "
        :class="{ 'hover:cursor-pointer hover:rounded-sm hover:bg-blue-50 hover:ring-4 hover:ring-blue-50': isClickable }">
        <UiIcon name="published" class="h-6 w-6 text-blue-500" />
        <span class="line-clamp-2 w-full text-sm">alle aus {{ eventName }} eingeladen</span>
    </div>
    <div
        v-else-if="!arePublicationsAvailable"
        class="flex items-center gap-1.5 justify-center py-1.5 px-2 bg-softred-100 text-xs">
        <UiIcon name="exclamation-circle" class="h-6 w-6 text-softred-800" /> Es ist noch niemand eingeladen!
    </div>
    <div
        v-else-if="twoOrganisationInvited"
        @[isClickable&&`click`]="action.internalPublicationShowInvitations()"
        :class="{ 'hover:cursor-pointer hover:rounded-sm hover:bg-blue-50 hover:ring-4 hover:ring-blue-50': isClickable }">
        <div v-for="publication in internalPublications" :key="publication.id" class="flex gap-2 ">
            <UiIcon name="published" class="h-6 w-6 text-blue-500" />
            <span class="line-clamp-2 w-full text-sm">
                {{ getDescription(publication) }}
            </span>
        </div>
    </div>
    <div
        v-else
        class="flex items-center gap-2 "
        :class="{ 'hover:cursor-pointer hover:rounded-sm hover:bg-blue-50 hover:ring-4 hover:ring-blue-50': isClickable }"
        @[isClickable&&`click`]="action.internalPublicationShowInvitations()">
        <UiIcon name="published" class="h-6 w-6 text-blue-500" />
        <span class="line-clamp-2 w-full text-sm">{{ internalPublications?.length }} Ziele eingeladen</span>
    </div>
    <button
        v-if="showButton"
        @click="action.internalPublicationInviteHelp()"
        class="flex justify-center items-center  form-button button-outline text-xs mt-2">
        <UiIcon name="user-add" class="h-4 w-4 text-blue-500" /> Helfende einladen
    </button>
</template>

<script lang="ts" setup>
    import { injectEventActions } from '~~/composables/event-actions'

    const props = withDefaults(defineProps<{
        internalPublications: ApiModel<'InternalPublication'>[] | ApiModel<'InternalPublication'>
        event: ApiModel<'Event'>
        isClickable?: boolean
        showButton?: boolean
    }>(), {
        showButton: false
    })

    const internalPublications = computed<ApiModel<'InternalPublication'>[]>(() => {
        return Array.isArray(props.internalPublications) ? props.internalPublications : [props.internalPublications]
    })

    const eventName = ref<string>(props.event?.organisation.name)

    const action = injectEventActions()

    const {
        oneOrganisationInvitationDescription,
        oneOrganisationInvited,
        arePublicationsAvailable,
        allOrganisationsInvited,
        twoOrganisationInvited,
        getDescription
    } = useInternalPublications(internalPublications)
</script>
