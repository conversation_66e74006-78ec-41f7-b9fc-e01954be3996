<template>
    <UiComposer :is-revealed="isRevealed" @cancel="cancel">
        <template #title>Planstelle bearbeiten</template>

        <template v-if="isRevealed">
            <EventPlanningCreateEventPostPersonalForm v-if="mode === Mode.Personal" v-model="_eventPost" />
            <EventPlanningCreateEventPostTechnicForm v-else-if="mode === Mode.Technic" v-model="_eventPost" />
        </template>

        <template #footer>
            <button @click="cancel" class="form-button button-contained-secondary">Abbrechen</button>
            <button class="form-button button-contained" @click="updateEventPost" :disabled="v$.$invalid || !dateInRange">Speichern</button>
        </template>
    </UiComposer>
</template>

<script lang="ts" setup>
    import useVuelidate from '@vuelidate/core'
    import { required, helpers } from '@vuelidate/validators'
    import { DialogController } from '~~/composables/dialog-controller'

    const props = defineProps<{
        controller: DialogController<'editEventPost'>
    }>()

    const { isRevealed, onReveal, cancel, confirm } = props.controller

    const _eventPost = ref<ApiModel<'EventPost'>>(null)
    const _event = ref<ApiModel<'Event'>>(null)
    const start = ref<Date>(null)
    const end = ref<Date>(null)

    onReveal(({eventPost, event}) => {
        _eventPost.value = useModelFactory('EventPost').create(eventPost)
        _event.value = useModelFactory('Event').create(event)
        start.value = event.dateFrom
        end.value = event.dateUpTo
    })

    enum Mode {
        Personal,
        Technic,
        Indifferent
    }

    const mode = computed(() => {
        switch (_eventPost.value?.eventPostResourceType) {
            case 'PERSONAL':
                return Mode.Personal

            case 'TECHNIC':
                return Mode.Technic

            default:
                return Mode.Indifferent
        }
    })

    const dateInRange = computed(() => {
        return start.value <=_eventPost.value?.dateFrom && end.value >=_eventPost.value?.dateUpTo && start.value < end.value
    })

    const validation = {
        description: { required },
    }

    const v$ = useVuelidate(validation, _eventPost)

    function updateEventPost() {
        confirm(_eventPost.value)
    }
</script>
